# 注意事项
1. 插件开发环境
	- **Windows:** visual studio 2019 以上
	- **MacOS:** xcode12.2以上
2. 插件文件都是 **cpm** 文件，可拷贝到对应目录下直接使用
	- **Windows：** C:\Users\<USER>\AppData\Roaming\CELSYSUserData\CELSYS\CLIPStudioModule\PlugIn\PAINT
	- **MacOS：** /Users/<USER>/Library/CELSYS/CLIPStudioModule/PlugIn/PAINT
	注：插件只能在 **clip studio paint ex** 中使用，且 **ex** 版本功能必须不受限。
3. 插件模块
	**clip studio paint** 主要是通过调用 **TriglavPluginCall**() 方法来运行过滤器
``` C++
/**
 * result out 处理成功赋值为 kTriglavPlugInCallResultSuccess，
              处理失败赋值为 kTriglavPlugInCallResultFailed
 * data in/out 用户可设定任意的值，且该值一直存在，直到插件运行结束
 * selector in 选择器(后面补充)
 * pluginServer in 插件服务器(后面补充)
 * reserved - 保留字段，以备将来使用
**/
void TRIGLAV_PLUGIN_API TriglavPluginCall( TriglavPlugInInt* result,
                                           TriglavPlugInPtr* data, 
                                           TriglavPlugInInt selector, 
                                           TriglavPlugInServer* pluginServer, 
                                           TriglavPlugInPtr reserved )
            
```
选择器——指示宿主请求的操作类型，插件会根据具体操作类型来分别处理。**selector** 可为下述常量之一：
``` c++
kTriglavPlugInSelectorModuleInitialize：实现插件模块初始化
kTriglavPlugInSelectorModuleTerminate：实现插件模块的终止处理
kTriglavPlugInSelectorFilterInitialize：实现过滤器初始化
kTriglavPlugInSelectorFilterTerminate：实现过滤器终止处理
kTriglavPlugInSelectorFilterRun：实现过滤器计算
```
插件服务器——提供了插件处理所需的函数，例如可以和插件一起使用的功能套件和宿主对象，下面是插件服务器结构：
``` c++
typedef struct _TriglavPlugInServer {
    TriglavPlugInRecordSuite recordSuite;    // 记录套件
    TriglavPlugInServiceSuite serviceSuite;  // 服务套件
    TriglavPlugInHostObject hostObject;      // 宿主对象
} TriglavPlugInServer;
```
        宿主对象：用于在插件主机端引用数据的结构  
        记录套件：提供一组对选择器值执行特殊处理的函数  
        服务套件：提供一组可与任何选择器值一起使用的函数
基本类型——插件模块中使用的基本类型都在文件 **TriglavPlugInType.h** 中声明，部分如下：
``` c++
typedef    unsigned char   TriglavPlugInBool;    // 可为 kTriglavPlugInBoolTrue 或 kTriglavPlugInBoolFalse；
typedef    char            TriglavPlugInChar;
typedef    unsigned short  TriglavPlugInUniChar;
typedef    unsigned char   TriglavPlugInUInt8;
typedef    long int        TriglavPlugInInt;
typedef    long long int   TriglavPlugInInt64;
typedef    float           TriglavPlugInFloat;
typedef    double          TriglavPlugInDouble;
typedef    void*           TriglavPlugInPtr;
```
预定义常量——插件中预定义的常量都在文件 TriglavPlugInDefine.h 中声明，部分如下：
``` c++
//	插件真值
#define	kTriglavPlugInBoolTrue  (1)
#define	kTriglavPlugInBoolFalse (0)
//	插件处理
#define	kTriglavPlugInSelectorModuleInitialize  (0x0101)
#define	kTriglavPlugInSelectorModuleTerminate   (0x0102)
#define	kTriglavPlugInSelectorFilterInitialize  (0x0201)
#define	kTriglavPlugInSelectorFilterRun         (0x0202)
#define	kTriglavPlugInSelectorFilterTerminate   (0x0203)
//	关屏复制模式
#define	kTriglavPlugInOffscreenCopyModeNormal   (0x01)
#define	kTriglavPlugInOffscreenCopyModeImage    (0x02)
#define	kTriglavPlugInOffscreenCopyModeGray     (0x03)
#define	kTriglavPlugInOffscreenCopyModeRed      (0x04)
#define	kTriglavPlugInOffscreenCopyModeGreen    (0x05)
#define	kTriglavPlugInOffscreenCopyModeBlue     (0x06)
#define	kTriglavPlugInOffscreenCopyModeCyan     (0x07)
#define	kTriglavPlugInOffscreenCopyModeMagenta  (0x08)
#define	kTriglavPlugInOffscreenCopyModeYellow   (0x09)
#define	kTriglavPlugInOffscreenCopyModeKeyPlate (0x10)
#define	kTriglavPlugInOffscreenCopyModeAlpha    (0x11)
```