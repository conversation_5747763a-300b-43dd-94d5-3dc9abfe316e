#include "TriglavPlugInSDK/TriglavPlugInSDK.h"
#include <assert.h>
#include <iostream>
#include <vector>
#include <thread>
#include <cmath>
#include "ColorHarmony.h"

typedef unsigned char BYTE;

// 属性项键值定义
static const int kItemColorSpace = 1;        // 色彩空间选择
static const int kItemHarmonyType = 2;       // 和谐类型选择
static const int kItemBaseHue = 3;           // 基础色相
static const int kItemBaseSaturation = 4;    // 基础饱和度
static const int kItemBaseLightness = 5;     // 基础亮度
static const int kItemLockLightness = 6;     // 亮度锁定
static const int kItemColorGamut = 7;        // 色域限制
static const int kItemApplyMode = 8;         // 应用模式
static const int kItemPreview = 9;           // 预览开关

// 本地化字符串ID
static const int kStringIDFilterCategoryName = 201;  // 滤镜分类名称
static const int kStringIDFilterName = 202;          // 滤镜名称
static const int kStringIDColorSpace = 203;          // 色彩空间
static const int kStringIDHarmonyType = 204;         // 和谐类型
static const int kStringIDBaseHue = 205;             // 基础色相
static const int kStringIDBaseSaturation = 206;      // 基础饱和度
static const int kStringIDBaseLightness = 207;       // 基础亮度
static const int kStringIDLockLightness = 208;       // 亮度锁定
static const int kStringIDColorGamut = 209;          // 色域限制
static const int kStringIDApplyMode = 210;           // 应用模式
static const int kStringIDPreview = 211;             // 预览

// 插件唯一标识符
static const char* uuidOfThisPlugin = "2F9A8B47-3C1D-4E5F-8A9B-1C2D3E4F5A6B";

// 插件信息结构体
struct ColorHarmonyFilterInfo
{
    int colorSpace;          // 当前色彩空间 (0=RGB, 1=HSV, 2=HSL, 3=LAB, 4=CMYK, 5=RYB)
    int harmonyType;         // 和谐类型 (0=互补, 1=邻近, 2=对比, 3=三角, 4=四角)
    double baseHue;          // 基础色相 (0-360)
    double baseSaturation;   // 基础饱和度 (0-100)
    double baseLightness;    // 基础亮度 (0-100)
    bool lockLightness;      // 是否锁定亮度
    int colorGamut;          // 色域限制 (0=无限制, 1=sRGB, 2=Adobe RGB, 3=自定义)
    int applyMode;           // 应用模式 (0=替换, 1=混合, 2=渐变)
    bool preview;            // 预览开关
    
    TriglavPlugInPropertyService* pPropertyService;
    TriglavPlugInPropertyService2* pPropertyService2;
};

// 属性回调函数
static void TRIGLAV_PLUGIN_CALLBACK TriglavPlugInFilterPropertyCallBack(
    TriglavPlugInInt* result, 
    TriglavPlugInPropertyObject propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInInt notify, 
    TriglavPlugInPtr data)
{
    (*result) = kTriglavPlugInPropertyCallBackResultNoModify;
    
    ColorHarmonyFilterInfo* pFilterInfo = static_cast<ColorHarmonyFilterInfo*>(data);
    
    if (pFilterInfo != NULL && pFilterInfo->pPropertyService != NULL)
    {
        // 当属性值发生变化时
        if (notify == kTriglavPlugInPropertyCallBackNotifyValueChanged)
        {
            // 获取当前所有属性值
            TriglavPlugInInt colorSpace, harmonyType, colorGamut, applyMode;
            TriglavPlugInDouble baseHue, baseSaturation, baseLightness;
            TriglavPlugInBool lockLightness, preview;
            
            (*pFilterInfo->pPropertyService2).getEnumerationValueProc(propertyObject, kItemColorSpace, &colorSpace);
            (*pFilterInfo->pPropertyService2).getEnumerationValueProc(propertyObject, kItemHarmonyType, &harmonyType);
            (*pFilterInfo->pPropertyService).getDecimalValueProc(propertyObject, kItemBaseHue, &baseHue);
            (*pFilterInfo->pPropertyService).getDecimalValueProc(propertyObject, kItemBaseSaturation, &baseSaturation);
            (*pFilterInfo->pPropertyService).getDecimalValueProc(propertyObject, kItemBaseLightness, &baseLightness);
            (*pFilterInfo->pPropertyService).getBooleanValueProc(propertyObject, kItemLockLightness, &lockLightness);
            (*pFilterInfo->pPropertyService2).getEnumerationValueProc(propertyObject, kItemColorGamut, &colorGamut);
            (*pFilterInfo->pPropertyService2).getEnumerationValueProc(propertyObject, kItemApplyMode, &applyMode);
            (*pFilterInfo->pPropertyService).getBooleanValueProc(propertyObject, kItemPreview, &preview);
            
            // 更新内部状态
            pFilterInfo->colorSpace = colorSpace;
            pFilterInfo->harmonyType = harmonyType;
            pFilterInfo->baseHue = baseHue;
            pFilterInfo->baseSaturation = baseSaturation;
            pFilterInfo->baseLightness = baseLightness;
            pFilterInfo->lockLightness = (lockLightness == kTriglavPlugInBoolTrue);
            pFilterInfo->colorGamut = colorGamut;
            pFilterInfo->applyMode = applyMode;
            pFilterInfo->preview = (preview == kTriglavPlugInBoolTrue);
            
            // 通知颜色和谐引擎参数变化
            if (pFilterInfo->preview)
            {
                ColorHarmony::GetInstance().OnParametersChanged(
                    pFilterInfo->colorSpace,
                    pFilterInfo->harmonyType,
                    pFilterInfo->baseHue,
                    pFilterInfo->baseSaturation,
                    pFilterInfo->baseLightness,
                    pFilterInfo->lockLightness,
                    pFilterInfo->colorGamut,
                    pFilterInfo->applyMode
                );
            }
            
            (*result) = kTriglavPlugInPropertyCallBackResultModify;
        }
    }
}

// 插件主入口函数
void TRIGLAV_PLUGIN_API TriglavPluginCall(
    TriglavPlugInInt* result, 
    TriglavPlugInPtr* data, 
    TriglavPlugInInt selector, 
    TriglavPlugInServer* pluginServer, 
    TriglavPlugInPtr reserved)
{
    *result = kTriglavPlugInCallResultFailed;
    
    if (pluginServer != NULL)
    {
        if (selector == kTriglavPlugInSelectorModuleInitialize)
        {
            // 模块初始化
            TriglavPlugInModuleInitializeRecord* pModuleInitializeRecord = (*pluginServer).recordSuite.moduleInitializeRecord;
            TriglavPlugInStringService* pStringService = (*pluginServer).serviceSuite.stringService;
            
            if (pModuleInitializeRecord != NULL && pStringService != NULL)
            {
                TriglavPlugInInt hostVersion;
                (*pModuleInitializeRecord).getHostVersionProc(&hostVersion, (*pluginServer).hostObject);
                
                if (hostVersion >= kTriglavPlugInNeedHostVersion)
                {
                    // 设置模块ID
                    TriglavPlugInStringObject moduleID = NULL;
                    (*pStringService).createWithAsciiStringProc(&moduleID, uuidOfThisPlugin, 
                        static_cast<TriglavPlugInInt>(::strlen(uuidOfThisPlugin)));
                    
                    (*pModuleInitializeRecord).setModuleIDProc((*pluginServer).hostObject, moduleID);
                    (*pModuleInitializeRecord).setModuleKindProc((*pluginServer).hostObject, 
                        kTriglavPlugInModuleSwitchKindFilter);
                    
                    (*pStringService).releaseProc(moduleID);
                    
                    // 创建插件信息结构
                    ColorHarmonyFilterInfo* pFilterInfo = new ColorHarmonyFilterInfo;
                    *data = pFilterInfo;
                    *result = kTriglavPlugInCallResultSuccess;
                }
            }
        }
        else if (selector == kTriglavPlugInSelectorModuleTerminate)
        {
            // 模块终止
            ColorHarmonyFilterInfo* pFilterInfo = static_cast<ColorHarmonyFilterInfo*>(*data);
            delete pFilterInfo;
            *data = NULL;
            *result = kTriglavPlugInCallResultSuccess;
            
            // 清理颜色和谐引擎
            ColorHarmony::PluginModuleCleanUp();
        }
        else if (selector == kTriglavPlugInSelectorFilterInitialize)
        {
            // 滤镜初始化 - 设置UI和属性
            // 这部分代码较长，将在下一个文件中继续实现
            *result = kTriglavPlugInCallResultSuccess;
        }
        else if (selector == kTriglavPlugInSelectorFilterTerminate)
        {
            // 滤镜终止
            *result = kTriglavPlugInCallResultSuccess;
        }
        else if (selector == kTriglavPlugInSelectorFilterRun)
        {
            // 滤镜运行 - 执行颜色处理
            // 这部分代码较长，将在下一个文件中继续实现
            *result = kTriglavPlugInCallResultSuccess;
        }
    }
    
    return;
}
