cmake_minimum_required(VERSION 3.20)

project(ColorHarmony VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt6
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Gui)

# 启用Qt的MOC、UIC、RCC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# 包含目录
include_directories(src)

# 源文件
set(SOURCES
    src/main.cpp
    src/MainWindow.cpp
    src/core/ColorConverter.cpp
    src/core/HarmonyEngine.cpp
    src/core/PaletteModel.cpp
    src/widgets/ColorPicker.cpp
    src/widgets/ColorWheel.cpp
    src/widgets/PaletteWidget.cpp
    src/utils/FileManager.cpp
    src/utils/ClipboardManager.cpp
)

# 头文件
set(HEADERS
    src/MainWindow.h
    src/core/ColorConverter.h
    src/core/HarmonyEngine.h
    src/core/PaletteModel.h
    src/widgets/ColorPicker.h
    src/widgets/ColorWheel.h
    src/widgets/PaletteWidget.h
    src/utils/FileManager.h
    src/utils/ClipboardManager.h
)

# UI文件
set(UI_FILES
    src/MainWindow.ui
    src/widgets/ColorPicker.ui
)

# 资源文件
set(RESOURCES
    resources/resources.qrc
)

# 创建可执行文件
add_executable(ColorHarmony
    ${SOURCES}
    ${HEADERS}
    ${UI_FILES}
    ${RESOURCES}
)

# 链接Qt库
target_link_libraries(ColorHarmony
    Qt6::Core
    Qt6::Widgets
    Qt6::Gui
)

# 设置输出目录
set_target_properties(ColorHarmony PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Windows特定设置
if(WIN32)
    set_target_properties(ColorHarmony PROPERTIES
        WIN32_EXECUTABLE TRUE
    )
    
    # 设置应用程序图标
    if(EXISTS "${CMAKE_SOURCE_DIR}/resources/icons/app.ico")
        set(APP_ICON "${CMAKE_SOURCE_DIR}/resources/icons/app.ico")
        target_sources(ColorHarmony PRIVATE ${APP_ICON})
    endif()
endif()

# macOS特定设置
if(APPLE)
    set_target_properties(ColorHarmony PROPERTIES
        MACOSX_BUNDLE TRUE
        MACOSX_BUNDLE_INFO_PLIST ${CMAKE_SOURCE_DIR}/resources/Info.plist
    )
endif()

# 安装规则
install(TARGETS ColorHarmony
    BUNDLE DESTINATION .
    RUNTIME DESTINATION bin
)

# Qt部署
if(WIN32)
    find_program(QT_WINDEPLOYQT_EXECUTABLE windeployqt HINTS ${Qt6_DIR}/../../../bin)
    if(QT_WINDEPLOYQT_EXECUTABLE)
        add_custom_command(TARGET ColorHarmony POST_BUILD
            COMMAND ${QT_WINDEPLOYQT_EXECUTABLE} $<TARGET_FILE:ColorHarmony>
            COMMENT "Deploying Qt libraries"
        )
    endif()
endif()

# 测试
enable_testing()
add_subdirectory(tests)

# 文档生成 (可选)
find_package(Doxygen)
if(DOXYGEN_FOUND)
    configure_file(${CMAKE_CURRENT_SOURCE_DIR}/docs/Doxyfile.in 
                   ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile @ONLY)
    add_custom_target(doc
        ${DOXYGEN_EXECUTABLE} ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Generating API documentation with Doxygen" VERBATIM
    )
endif()

# 编译选项
if(MSVC)
    target_compile_options(ColorHarmony PRIVATE /W4)
else()
    target_compile_options(ColorHarmony PRIVATE -Wall -Wextra -Wpedantic)
endif()

# Debug配置
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(ColorHarmony PRIVATE DEBUG)
endif()

# 版本信息
configure_file(
    "${CMAKE_SOURCE_DIR}/src/version.h.in"
    "${CMAKE_BINARY_DIR}/version.h"
)
target_include_directories(ColorHarmony PRIVATE ${CMAKE_BINARY_DIR})
