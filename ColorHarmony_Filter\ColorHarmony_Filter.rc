#include "resource.h"
#include "windows.h"

/////////////////////////////////////////////////////////////////////////////
//
// String Table
//

STRINGTABLE
BEGIN
    201                     "Color Tools"
    202                     "Color Harmony"
    203                     "Color Space"
    204                     "Harmony Type"
    205                     "Base Hue"
    206                     "Base Saturation"
    207                     "Base Lightness"
    208                     "Lock Lightness"
    209                     "Color Gamut"
    210                     "Apply Mode"
    211                     "Preview"
    
    // Color Space Options
    301                     "RGB"
    302                     "HSV"
    303                     "HSL"
    304                     "LAB"
    305                     "CMYK"
    306                     "RYB"
    
    // Harmony Type Options
    401                     "Complementary"
    402                     "Analogous"
    403                     "Triadic"
    404                     "Tetradic"
    405                     "Split Complementary"
    406                     "Monochromatic"
    
    // Color Gamut Options
    501                     "No Limit"
    502                     "sRGB"
    503                     "Adobe RGB"
    504                     "Custom"
    
    // Apply Mode Options
    601                     "Replace"
    602                     "Blend"
    603                     "Gradient"
END

/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION 1,0,0,1
 PRODUCTVERSION 1,0,0,1
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x40004L
 FILETYPE 0x2L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", "CSP Color Tools"
            VALUE "FileDescription", "Color Harmony Filter for Clip Studio Paint"
            VALUE "FileVersion", "*******"
            VALUE "InternalName", "ColorHarmony_Filter.cpm"
            VALUE "LegalCopyright", "Copyright (C) 2024"
            VALUE "OriginalFilename", "ColorHarmony_Filter.cpm"
            VALUE "ProductName", "Color Harmony Filter"
            VALUE "ProductVersion", "*******"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END
