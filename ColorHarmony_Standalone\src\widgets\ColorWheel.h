#pragma once

#include <QWidget>
#include <QColor>
#include <QPainter>
#include <QPaintEvent>
#include <QMouseEvent>
#include <QWheelEvent>
#include <QResizeEvent>
#include <QTimer>
#include <QPropertyAnimation>
#include <QEasingCurve>
#include <QPixmap>
#include <QConicalGradient>
#include <QRadialGradient>
#include <QLinearGradient>
#include <QPointF>
#include <QRectF>
#include <QSizeF>
#include <cmath>

#include "../core/ColorConverter.h"

// 现代化颜色轮组件 - Windows 11风格
class ColorWheel : public QWidget
{
    Q_OBJECT
    Q_PROPERTY(QColor color READ color WRITE setColor NOTIFY colorChanged)
    Q_PROPERTY(double hue READ hue WRITE setHue NOTIFY hueChanged)
    Q_PROPERTY(double saturation READ saturation WRITE setSaturation NOTIFY saturationChanged)
    Q_PROPERTY(double value READ value WRITE setValue NOTIFY valueChanged)
    Q_PROPERTY(ColorSpace colorSpace READ colorSpace WRITE setColorSpace NOTIFY colorSpaceChanged)
    Q_PROPERTY(bool animated READ isAnimated WRITE setAnimated)
    Q_PROPERTY(int wheelWidth READ wheelWidth WRITE setWheelWidth)
    Q_PROPERTY(bool showValueTriangle READ showValueTriangle WRITE setShowValueTriangle)

public:
    explicit ColorWheel(QWidget *parent = nullptr);
    ~ColorWheel();

    // 颜色属性
    QColor color() const { return m_color; }
    double hue() const { return m_hue; }
    double saturation() const { return m_saturation; }
    double value() const { return m_value; }
    
    // 显示设置
    ColorSpace colorSpace() const { return m_colorSpace; }
    bool isAnimated() const { return m_animated; }
    int wheelWidth() const { return m_wheelWidth; }
    bool showValueTriangle() const { return m_showValueTriangle; }
    
    // 尺寸相关
    QSize sizeHint() const override;
    QSize minimumSizeHint() const override;

public slots:
    void setColor(const QColor& color);
    void setHue(double hue);
    void setSaturation(double saturation);
    void setValue(double value);
    void setHSV(double h, double s, double v);
    void setColorSpace(ColorSpace space);
    void setAnimated(bool animated);
    void setWheelWidth(int width);
    void setShowValueTriangle(bool show);
    
    // 动画控制
    void animateToColor(const QColor& color, int duration = 300);
    void resetToCenter();

signals:
    void colorChanged(const QColor& color);
    void hueChanged(double hue);
    void saturationChanged(double saturation);
    void valueChanged(double value);
    void colorSpaceChanged(ColorSpace space);
    void colorPicked(const QColor& color);
    void colorDragging(const QColor& color);

protected:
    void paintEvent(QPaintEvent* event) override;
    void mousePressEvent(QMouseEvent* event) override;
    void mouseMoveEvent(QMouseEvent* event) override;
    void mouseReleaseEvent(QMouseEvent* event) override;
    void wheelEvent(QWheelEvent* event) override;
    void resizeEvent(QResizeEvent* event) override;
    void leaveEvent(QEvent* event) override;
    void enterEvent(QEnterEvent* event) override;

private slots:
    void onAnimationFinished();
    void onHoverTimerTimeout();

private:
    // 绘制函数
    void drawColorWheel(QPainter& painter);
    void drawValueTriangle(QPainter& painter);
    void drawHueIndicator(QPainter& painter);
    void drawSaturationValueIndicator(QPainter& painter);
    void drawCenterPreview(QPainter& painter);
    void drawHoverEffect(QPainter& painter);
    void drawSelectionRing(QPainter& painter);
    
    // 几何计算
    QPointF hueToPoint(double hue) const;
    double pointToHue(const QPointF& point) const;
    QPointF svToTrianglePoint(double saturation, double value) const;
    void trianglePointToSV(const QPointF& point, double& saturation, double& value) const;
    
    // 区域检测
    bool isInHueRing(const QPointF& point) const;
    bool isInValueTriangle(const QPointF& point) const;
    bool isInCenterPreview(const QPointF& point) const;
    
    // 缓存管理
    void updateWheelCache();
    void updateTriangleCache();
    void invalidateCache();
    
    // 颜色转换
    void updateFromColor();
    void updateColorFromHSV();
    QColor getColorAtPoint(const QPointF& point) const;
    
    // 动画相关
    void setupAnimations();
    void startColorAnimation(const QColor& targetColor);
    
    // 数据成员
    QColor m_color;
    double m_hue;           // 0-360
    double m_saturation;    // 0-1
    double m_value;         // 0-1
    
    // 显示设置
    ColorSpace m_colorSpace;
    bool m_animated;
    int m_wheelWidth;
    bool m_showValueTriangle;
    
    // 几何参数
    QPointF m_center;
    double m_outerRadius;
    double m_innerRadius;
    QRectF m_wheelRect;
    QRectF m_triangleRect;
    QRectF m_centerRect;
    
    // 交互状态
    bool m_draggingHue;
    bool m_draggingSV;
    bool m_hovering;
    QPointF m_lastMousePos;
    QPointF m_hoverPos;
    
    // 缓存
    QPixmap m_wheelCache;
    QPixmap m_triangleCache;
    bool m_wheelCacheValid;
    bool m_triangleCacheValid;
    
    // 动画
    QPropertyAnimation* m_hueAnimation;
    QPropertyAnimation* m_saturationAnimation;
    QPropertyAnimation* m_valueAnimation;
    QTimer* m_hoverTimer;
    
    // 样式参数
    static const int DEFAULT_WHEEL_WIDTH = 30;
    static const int MIN_RADIUS = 80;
    static const int CENTER_PREVIEW_RADIUS = 20;
    static const int INDICATOR_SIZE = 12;
    static const int SELECTION_RING_WIDTH = 3;
    static const double TRIANGLE_MARGIN = 10.0;
    
    // 颜色常量
    static const QColor INDICATOR_COLOR;
    static const QColor SELECTION_COLOR;
    static const QColor HOVER_COLOR;
    static const QColor BORDER_COLOR;
};
