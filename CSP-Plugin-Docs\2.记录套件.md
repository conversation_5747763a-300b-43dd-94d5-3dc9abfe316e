记录套件端提供了可以在特定条件下使用的函数。返回值为 **TriglavPlugInInt** 型常数，为 **kTriglavPlugInAPIResultSuccess** 表明返回成功，为 **kTriglavPlugInAPIResultFailed** 表明失败。
在介绍各个 **api** 之前，先看看插件服务器与记录套件结构体分别是如何定义的：
```c++
// 插件服务器
typedef	struct	_TriglavPlugInServer {
	TriglavPlugInRecordSuite		recordSuite;
	TriglavPlugInServiceSuite		serviceSuite;
	TriglavPlugInHostObject			hostObject;
} TriglavPlugInServer;
// 记录套件
typedef	struct	_TriglavPlugInRecordSuite {
	TriglavPlugInModuleInitializeRecord*		moduleInitializeRecord;
	TriglavPlugInFilterInitializeRecord*		filterInitializeRecord;
	TriglavPlugInFilterRunRecord*				filterRunRecord;
	...
} TriglavPlugInRecordSuite;
```

这样就可以通过如下语句将插件服务器与具体记录套件关联起来了：

```c++
TriglavPlugInServer* pluginServer;
TriglavPlugInModuleInitializeRecord*	pModuleInitializeRecord	= (*pluginServer).recordSuite.moduleInitializeRecord;
```

# 一、模块初始化记录(**ModuleInitializeRecord**)
模块初始化记录结构体定义如下：
```c++
typedef	struct	_TriglavPlugInModuleInitializeRecord {
	TriglavPlugInModuleInitializeGetHostVersionProc	getHostVersionProc;
	TriglavPlugInModuleInitializeSetModuleIDProc 	setModuleIDProc;
	TriglavPlugInModuleInitializeSetModuleKindProc	setModuleKindProc;
} TriglavPlugInModuleInitializeRecord;
```

1. 获取指定宿主对象的宿主版本，若失败返回 0
``` c++
/**
 * hostVersion: 宿主版本
 * hostObject: 宿主对象
**/
TRIGLAV_PLUGIN_API *getHostVersionProc(
    TriglavPlugInInt* hostVersion,
    TriglavPlugInHostObject hostObject);
```

2. 在指定的宿主对象设置模块身份

```c++
/**
 * hostObject: 宿主对象
 * moduleID: 模块id
**/
TRIGLAV_PLUGIN_API *setModuleIDProc(
    TriglavPlugInHostObject hostObject,
    TriglavPlugInStringObject moduleID);
```

3. 在指定的宿主对象上设置模块类型，若失败，则参数 **hostObject** 中未设置模块类型。创建过滤器插件时，需将参数 **moduleKind** 设置为 **kTriglavPlugInModuleKindFilter**

```c++
/**
 * hostObject: 宿主对象
 * moduleKind: 模块类型
**/
TRIGLAV_PLUGIN_API *setModuleKindProc(
    TriglavPlugInHostObject hostObject,
    const TriglavPlugInInt moduleKind);
```

# 二、过滤器初始化记录(**FilterInitializeRecord**)

1. 获取过滤器初始化记录
```c++
/**
 * record: 记录套件
**/
TRIGLAV_PLUGIN_API *TriglavPlugInGetFilterInitializeRecord(
    TriglavPlugInRecordSuite record);
```

2. xxxxxxxxxx //  插件真值#define kTriglavPlugInBoolTrue  (1)#define kTriglavPlugInBoolFalse (0)//  插件处理#define kTriglavPlugInSelectorModuleInitialize  (0x0101)#define kTriglavPlugInSelectorModuleTerminate   (0x0102)#define kTriglavPlugInSelectorFilterInitialize  (0x0201)#define kTriglavPlugInSelectorFilterRun         (0x0202)#define kTriglavPlugInSelectorFilterTerminate   (0x0203)//  关屏复制模式#define kTriglavPlugInOffscreenCopyModeNormal   (0x01)#define kTriglavPlugInOffscreenCopyModeImage    (0x02)#define kTriglavPlugInOffscreenCopyModeGray     (0x03)#define kTriglavPlugInOffscreenCopyModeRed      (0x04)#define kTriglavPlugInOffscreenCopyModeGreen    (0x05)#define kTriglavPlugInOffscreenCopyModeBlue     (0x06)#define kTriglavPlugInOffscreenCopyModeCyan     (0x07)#define kTriglavPlugInOffscreenCopyModeMagenta  (0x08)#define kTriglavPlugInOffscreenCopyModeYellow   (0x09)#define kTriglavPlugInOffscreenCopyModeKeyPlate (0x10)#define kTriglavPlugInOffscreenCopyModeAlpha    (0x11)c++

```c++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * filterCategoryName: 过滤器类别名称
 * accessKey: 访问密钥
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterInitializeSetFilterCategoryName(
    TriglavPlugInRecordSuite record,
    TriglavPlugInHostObject hostObject,
    TriglavPlugInStringObject filterCategoryName,
    const TriglavPlugInChar accessKey)
```

3. 为指定的宿主对象设置过滤器名称和访问密钥
``` c++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * filterName: 过滤器名称
 * accessKey: 访问密钥
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterInitializeSetFilterName(
    TriglavPlugInRecordSuite record,
    TriglavPlugInHostObject hostObject,
    TriglavPlugInStringObject filterName,
    const TriglavPlugInChar accessKey)
```

4. 设置是否允许对指定的宿主对象进行预览显示
``` c++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * canPreview: 是否允许预览显示
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterInitializeSetCanPreview(
    TriglavPlugInRecordSuite record,
    TriglavPlugInHostObject hostObject,
    const TriglavPlugInBool canPreview)
```

5. 设置是否过滤指定的宿主对象，即使图像上没任何绘制内容
``` c++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * useBlankImage: 对于没有绘制任何的可填充图像，是否运行数据
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterInitializeSetUseBlankImage(
    TriglavPlugInRecordSuite record,
    TriglavPlugInHostObject hostObject,
    const TriglavPlugInBool useBlankImage)
```

6. 在指定的对象上设置目标层类型，可指定多个目标层
``` c++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * filterName: 过滤器名称
 * accessKey: 访问密钥
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterInitializeSetTargetKinds(
    TriglavPlugInRecordSuite record,
    TriglavPlugInHostObject hostObject,
    const TriglavPlugInInt* targetKinds,
    const TriglavPlugInInt targetKindCount)
```

目标层定义使用的常量：

``` c++
#define	kTriglavPlugInFilterTargetKindRasterLayerGrayAlpha				(0x0101)
#define	kTriglavPlugInFilterTargetKindRasterLayerRGBAlpha				(0x0102)
#define	kTriglavPlugInFilterTargetKindRasterLayerCMYKAlpha				(0x0103)
#define	kTriglavPlugInFilterTargetKindRasterLayerAlpha					(0x0104)
#define	kTriglavPlugInFilterTargetKindRasterLayerBinarizationAlpha		(0x0105)
#define	kTriglavPlugInFilterTargetKindRasterLayerBinarizationGrayAlpha	(0x0106)
```

 7. 在指定的宿主对象上设置一个属性对象

 ```c++
/**

 * record: 记录套件
 * hostObject: 宿主对象
 * propertyObject: 属性对象
   **/
   TRIGLAV_PLUGIN_API *TriglavPlugInFilterInitializeSetProperty(
    TriglavPlugInRecordSuite record,
    TriglavPlugInHostObject hostObject,
    TriglavPlugInPropertyObject propertyObject)
 ```
8. 在指定的宿主对象上设置属性回调函数的指针和属性回调中要使用的数据的指针

``` c++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * propertyCallBackProc: 属性回调函数
 * data: 数据
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterInitializeSetPropertyCallBack(
    TriglavPlugInRecordSuite record, 
    TriglavPlugInHostObject hostObject, 
    TriglavPlugInPropertyCallBackProc propertyCallBackProc, 
    TriglavPlugInPtr data)
```


# *三、 过滤器执行记录**

1. 获取过滤器执行记录
```c++
/**
 * record: 记录套件
**/
TRIGLAV_PLUGIN_API *TriglavPlugInGetFilterRunRecord(
    TriglavPlugInRecordSuite record);
```
2. 从指定的宿主对象中获取一个属性对象，若失败返回 null
```c++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * propertyObject: 属性对象
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterRunGetProperty(
    TriglavPlugInRecordSuite record,
    TriglavPlugInHostObject hostObject,
    TriglavPlugInPropertyObject* propertyObject)
```
3. 从指定的宿主对象获取画布宽度
```c++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * width: 画布宽度
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterRunGetCanvasWidth(
    TriglavPlugInRecordSuite record,
    TriglavPlugInHostObject hostObject,
    TriglavPlugInInt* width);
```
4. 从指定的宿主对象获取画布高度
```c++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * height: 画布高度
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterRunGetCanvasHeight(
    TriglavPlugInRecordSuite record,
    TriglavPlugInHostObject hostObject, 
    TriglavPlugInInt* height);
```
5. 从指定的宿主对象获取画布分辨率
```c++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * resolution: 画布分辨率
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterRunGetCanvasResolution(
    TriglavPlugInRecordSuite record, 
    TriglavPlugInHostObject hostObject, 
    TriglavPlugInInt* resolution);
```
6. 从指定的宿主对象获取编辑图层原点
```c++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * layerOrigin: 编辑图层原点
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterRunGetLayerOrigin(
    TriglavPlugInRecordSuite record, 
    TriglavPlugInHostObject hostObject, 
    TriglavPlugInPoint* layerOrigin);
```
7. 从指定的宿主对象判断是否选择了编辑图层的掩码。如果选择了，则 kTriglavPlugInBoolTrue 将替换所选参数；如果未选择，则 kTriglavPlugInBoolFalse 将替换所选参数；如果获取失败，则 kTriglavPlugInBoolFalse 将替换所选参数。

```c++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * selected: 是否选择图层蒙版
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterRunIsLayerMaskSelected(
    TriglavPlugInRecordSuite record, 
    TriglavPlugInHostObject hostObject, 
    TriglavPlugInBool* selected);
```
8. 从指定的宿主对象判断编辑图层的透明像素是否被锁定
```c++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * locked: 透明像素是否被锁定
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterRunIsAlphaLocked(
    TriglavPlugInRecordSuite record, 
    TriglavPlugInHostObject hostObject, 
    TriglavPlugInBool* locked);
```
9. 从指定的宿主对象获取执行过程的原始屏幕外资源
```c++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * offscreenObject: 屏幕外对象
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterRunGetSourceOffscreen(
    TriglavPlugInRecordSuite record, 
    TriglavPlugInHostObject hostObject, 
    TriglavPlugInOffscreenObject* offscreenObject);
```
10. 从指定的宿主对象获取执行过程的目标屏幕外资源
```c++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * offscreenObject: 屏幕外对象
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterRunGetDestinationOffscreen(
    TriglavPlugInRecordSuite record, 
    TriglavPlugInHostObject hostObject, 
    TriglavPlugInOffscreenObject* offscreenObject);
```
11. 从指定的宿主对象获取编辑图层是否有屏幕外选择
```c++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * hasSelectAreaOffscreen: 是否有屏幕外选择
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterRunHasSelectAreaOffscreen(
    TriglavPlugInRecordSuite record, 
    TriglavPlugInHostObject hostObject, 
    TriglavPlugInBool* hasSelectAreaOffscreen);
```
12. 从指定的离屏对象获取选择区域的边界矩形
``` c++
/**
 * record: 记录套件
 * offscreenObject: 离屏对象
 * rect: 选择区域的边界矩形（像素）
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterRunGetSelectAreaRect(
    TriglavPlugInRecordSuite record, 
    TriglavPlugInOffscreenObject offscreenObject, 
    TriglavPlugInRect* rect);
```
从指定的宿主对象获取屏幕外选择的区域对象，可以使用函数 **TriglavPlugInFilterRunHasSelectAreaOffscreen**()。
13. 检查是否存在屏幕外选择
```c++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * offscreenObject: 离屏对象
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterRunGetSelectAreaOffscreen(
    TriglavPlugInRecordSuite record, 
    TriglavPlugInHostObject hostObject, 
    TriglavPlugInOffscreenObject* offscreenObject);
```

14. 更新指定宿主对象的目标屏幕外
```c++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * updateRect: 更新范围
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterRunUpdateDestinationOffscreenRect(
    TriglavPlugInRecordSuite record, 
    TriglavPlugInHostObject hostObject, 
    const TrigravPlugInRect* updateRect);
```

15. 从指定的宿主对象获取当前主颜色
```C++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * mainColor: 主色调
 * mainAlpha: 主透明度
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterRunGetMainColor(
    TriglavPlugInRecordSuite record, 
    TriglavPlugInHostObject hostObject, 
    TriglavPlugInRGBColor* mainColor, 
    TriglavPlugInUInt8* mainAlpha);
```

16. 从指定的宿主对象获取当前子颜色

``` C++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * subColor: 子色调
 * subAlpha: 子透明度
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterRunGetSubColor(
    TriglavPlugInRecordSuite record, 
    TriglavPlugInHostObject hostObject, 
    TriglavPlugInRGBColor* subColor, 
    TriglavPlugInUInt8* subAlpha);
```

17. 从指定的宿主对象获取当前的绘图颜色

```C++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * drawColor: 绘图颜色
 * drawAlpha: 绘图透明度
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterRunGetDrawColor(
    TriglavPlugInRecordSuite record, 
    TriglavPlugInHostObject hostObject, 
    TriglavPlugInRGBColor* drawColor, 
    TriglavPlugInUInt8* drawAlpha);
```

18. 从指定插件的过滤运行状态获取宿主对象的过滤器运行状态
``` C++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * processState: 插件模块的过滤器运行状态
 * result: 宿主对象的过滤器运行状态
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterRunProcess(
    TriglavPlugInRecordSuite record, 
    TriglavPlugInHostObject hostObject, 
    const TriglavPlugInInt processState, 
    TriglavPlugInInt* result);
```

指示参数 **processState** 的状态及其对应的参数结果，开发者必须根据参数结果的结果进行适当的处理。

| processState                                | result                                       | note                |
| :------------------------------------------ | :------------------------------------------- | :------------------ |
| kTriglavPluginFilterRunProcessStateStart    | kTriglavPluginFilterRunProcessResultRestart  | 处理需要重新计算            |
|                                             | kTriglavPluginFilterRunProcessResultContinue | 继续处理                |
|                                             | kTriglavPluginFilterRunProcessResultExit     | 需要完成处理（取消按钮或×按钮被按下） |
| kTriglavPluginFilterRunProcessStateContinue | kTriglavPluginFilterRunProcessResultRestart  | 处理需要重新计算            |
|                                             | kTriglavPluginFilterRunProcessResultContinue | 继续处理                |
|                                             | kTriglavPluginFilterRunProcessResultExit     | 需要完成处理（取消按钮或×按钮被按下） |
| kTriglavPluginFilterRunProcessStateEnd      | kTriglavPluginFilterRunProcessResultRestart  | 处理需要重新计算            |
|                                             | kTriglavPluginFilterRunProcessResultExit     | 需要结束处理（按下确定按钮）      |
| kTriglavPluginFilterRunProcessStateAbort    | kTriglavPluginFilterRunProcessResultContinue | 继续处理                |
|                                             | kTriglavPluginFilterRunProcessResultExit     | 需要完成处理（取消按钮或×按钮被按下） |
19. 设置指定宿主对象的总进度
```C++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * progressTotal: 总进度
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterRunSetProgressTotal(
    TriglavPlugInRecordSuite record, 
    TriglavPlugInHostObject hostObject, 
    const TriglavPlugInInt progressTotal);
```

20. 设置指定宿主对象的进度
```C++
/**
 * record: 记录套件
 * hostObject: 宿主对象
 * progressState: 进度
**/
TRIGLAV_PLUGIN_API *TriglavPlugInFilterRunSetProgressDone(
    TriglavPlugInRecordSuite record, 
    TriglavPlugInHostObject hostObject, 
    const TriglavPlugInInt progressState);
```