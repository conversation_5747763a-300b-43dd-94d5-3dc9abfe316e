# Color Harmony - 编译指南

## 系统要求

### Windows
- **操作系统**: Windows 10 1909+ 或 Windows 11
- **编译器**: Visual Studio 2019/2022 或 MinGW-w64 8.0+
- **Qt版本**: Qt 6.5.0 或更高版本
- **CMake**: 3.20 或更高版本

### macOS
- **操作系统**: macOS 10.15+ (Catalina)
- **编译器**: Xcode 12.2+ 或 Clang 11+
- **Qt版本**: Qt 6.5.0 或更高版本
- **CMake**: 3.20 或更高版本

### Linux
- **操作系统**: Ubuntu 20.04+, CentOS 8+, 或其他现代发行版
- **编译器**: GCC 9+ 或 Clang 11+
- **Qt版本**: Qt 6.5.0 或更高版本
- **CMake**: 3.20 或更高版本

## 依赖安装

### Windows

#### 方法1: 使用Qt在线安装器 (推荐)
1. 下载Qt在线安装器: https://www.qt.io/download-qt-installer
2. 安装Qt 6.5.x，确保选择以下组件：
   - Qt 6.5.x for MSVC 2019/2022 64-bit
   - Qt Creator
   - CMake
   - Ninja

#### 方法2: 使用vcpkg
```powershell
# 安装vcpkg
git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg
.\bootstrap-vcpkg.bat

# 安装Qt6
.\vcpkg install qt6-base:x64-windows
.\vcpkg install qt6-tools:x64-windows
```

### macOS

#### 使用Homebrew (推荐)
```bash
# 安装Homebrew (如果未安装)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装依赖
brew install qt6 cmake ninja
```

### Linux (Ubuntu/Debian)
```bash
# 更新包列表
sudo apt update

# 安装Qt6和构建工具
sudo apt install qt6-base-dev qt6-tools-dev cmake ninja-build build-essential

# 安装额外的Qt6模块
sudo apt install qt6-base-dev-tools libqt6core6 libqt6gui6 libqt6widgets6
```

### Linux (CentOS/RHEL/Fedora)
```bash
# CentOS/RHEL
sudo dnf install qt6-qtbase-devel qt6-qttools-devel cmake ninja-build gcc-c++

# Fedora
sudo dnf install qt6-qtbase-devel qt6-qttools-devel cmake ninja-build gcc-c++
```

## 编译步骤

### 1. 克隆或下载源代码
```bash
# 如果使用Git
git clone <repository-url> ColorHarmony
cd ColorHarmony/ColorHarmony_Standalone

# 或者直接进入项目目录
cd ColorHarmony_Standalone
```

### 2. 创建构建目录
```bash
mkdir build
cd build
```

### 3. 配置项目

#### Windows (Visual Studio)
```powershell
# 使用Visual Studio 2022
cmake .. -G "Visual Studio 17 2022" -A x64 -DCMAKE_PREFIX_PATH="C:\Qt\6.5.0\msvc2022_64"

# 使用Visual Studio 2019
cmake .. -G "Visual Studio 16 2019" -A x64 -DCMAKE_PREFIX_PATH="C:\Qt\6.5.0\msvc2019_64"
```

#### Windows (Ninja)
```powershell
cmake .. -G Ninja -DCMAKE_BUILD_TYPE=Release -DCMAKE_PREFIX_PATH="C:\Qt\6.5.0\msvc2022_64"
```

#### macOS
```bash
cmake .. -G Ninja -DCMAKE_BUILD_TYPE=Release -DCMAKE_PREFIX_PATH="/usr/local/opt/qt6"
```

#### Linux
```bash
cmake .. -G Ninja -DCMAKE_BUILD_TYPE=Release
```

### 4. 编译项目

#### Windows (Visual Studio)
```powershell
# 命令行编译
cmake --build . --config Release

# 或者打开Visual Studio解决方案
start ColorHarmony.sln
```

#### 其他平台
```bash
cmake --build . --config Release
```

### 5. 运行程序

#### Windows
```powershell
# 从构建目录运行
.\bin\Release\ColorHarmony.exe

# 或者
cd bin\Release
.\ColorHarmony.exe
```

#### macOS/Linux
```bash
# 从构建目录运行
./bin/ColorHarmony

# 或者
cd bin
./ColorHarmony
```

## 开发环境设置

### Qt Creator (推荐)
1. 打开Qt Creator
2. 选择 "Open Project"
3. 选择项目根目录下的 `CMakeLists.txt`
4. 配置构建套件 (Build Kit)
5. 点击 "Configure Project"
6. 按 Ctrl+R 运行项目

### Visual Studio Code
1. 安装以下扩展：
   - C/C++ Extension Pack
   - CMake Tools
   - Qt tools
2. 打开项目文件夹
3. 按 Ctrl+Shift+P，选择 "CMake: Configure"
4. 按 F5 调试运行

### Visual Studio
1. 使用 "Open Folder" 功能打开项目目录
2. Visual Studio会自动检测CMake项目
3. 选择构建配置并编译

## 常见问题

### Qt找不到
```bash
# 手动指定Qt路径
cmake .. -DCMAKE_PREFIX_PATH="/path/to/qt6"
```

### 编译错误：找不到头文件
确保安装了完整的Qt6开发包：
```bash
# Ubuntu
sudo apt install qt6-base-dev qt6-tools-dev

# macOS
brew install qt6

# Windows
# 确保Qt安装器中选择了开发工具
```

### 运行时错误：找不到DLL/共享库
#### Windows
将Qt的bin目录添加到PATH，或使用windeployqt：
```powershell
windeployqt.exe ColorHarmony.exe
```

#### Linux
```bash
export LD_LIBRARY_PATH=/path/to/qt6/lib:$LD_LIBRARY_PATH
```

#### macOS
```bash
export DYLD_LIBRARY_PATH=/path/to/qt6/lib:$DYLD_LIBRARY_PATH
```

## 打包发布

### Windows
```powershell
# 使用windeployqt自动打包
windeployqt.exe --release --no-translations ColorHarmony.exe

# 创建安装包 (需要NSIS)
cpack -G NSIS
```

### macOS
```bash
# 创建应用包
macdeployqt ColorHarmony.app

# 创建DMG
cpack -G DragNDrop
```

### Linux
```bash
# 创建AppImage
linuxdeployqt ColorHarmony -appimage

# 创建DEB包
cpack -G DEB

# 创建RPM包
cpack -G RPM
```

## 性能优化

### Release构建
```bash
cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_CXX_FLAGS="-O3 -DNDEBUG"
```

### 链接时优化 (LTO)
```bash
cmake .. -DCMAKE_INTERPROCEDURAL_OPTIMIZATION=ON
```

### 静态链接 (减少依赖)
```bash
cmake .. -DCMAKE_BUILD_TYPE=Release -DQT_STATIC_BUILD=ON
```

## 故障排除

如果遇到编译问题，请检查：
1. Qt版本是否为6.5.0或更高
2. CMake版本是否为3.20或更高
3. 编译器是否支持C++17
4. 所有依赖是否正确安装

如需帮助，请提供：
- 操作系统和版本
- Qt版本
- CMake版本
- 完整的错误信息
