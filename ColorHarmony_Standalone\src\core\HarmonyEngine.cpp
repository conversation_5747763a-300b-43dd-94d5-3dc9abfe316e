#include "HarmonyEngine.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QDebug>
#include <QThread>
#include <QMutex>
#include <QMutexLocker>
#include <algorithm>
#include <random>
#include <cmath>

// 和谐类型名称映射
const QMap<HarmonyType, QString> HarmonyEngine::HARMONY_TYPE_NAMES = {
    {HarmonyType::Complementary, "Complementary"},
    {HarmonyType::Analogous, "Analogous"},
    {HarmonyType::Triadic, "Triadic"},
    {HarmonyType::Tetradic, "Tetradic"},
    {HarmonyType::SplitComplementary, "Split Complementary"},
    {HarmonyType::Monochromatic, "Monochromatic"},
    {HarmonyType::Square, "Square"},
    {HarmonyType::DoubleSplitComplementary, "Double Split Complementary"},
    {HarmonyType::Custom, "Custom"}
};

// 和谐类型描述
const QMap<HarmonyType, QString> HarmonyEngine::HARMONY_DESCRIPTIONS = {
    {HarmonyType::Complementary, "Colors that are opposite each other on the color wheel"},
    {HarmonyType::Analogous, "Colors that are next to each other on the color wheel"},
    {HarmonyType::Triadic, "Three colors equally spaced around the color wheel"},
    {HarmonyType::Tetradic, "Four colors arranged into two complementary pairs"},
    {HarmonyType::SplitComplementary, "A base color and the two colors adjacent to its complement"},
    {HarmonyType::Monochromatic, "Different shades, tints, and tones of a single color"},
    {HarmonyType::Square, "Four colors evenly spaced around the color wheel"},
    {HarmonyType::DoubleSplitComplementary, "Two pairs of split complementary colors"},
    {HarmonyType::Custom, "User-defined color harmony"}
};

// 黄金比例常量
const QVector<double> HarmonyEngine::GOLDEN_RATIOS = {
    1.618, 0.618, 2.618, 0.382, 1.272, 0.786
};

// 异步工作线程类
class HarmonyEngine::HarmonyWorker : public QObject
{
    Q_OBJECT

public:
    HarmonyWorker(HarmonyEngine* parent) : m_parent(parent), m_cancelled(false) {}
    
    void generateHarmony(const QColor& baseColor, const HarmonyConfig& config)
    {
        m_cancelled = false;
        m_baseColor = baseColor;
        m_config = config;
        
        QThread* thread = new QThread;
        this->moveToThread(thread);
        
        connect(thread, &QThread::started, this, &HarmonyWorker::process);
        connect(this, &HarmonyWorker::finished, thread, &QThread::quit);
        connect(thread, &QThread::finished, thread, &QThread::deleteLater);
        
        thread->start();
    }
    
    void cancel() { m_cancelled = true; }

public slots:
    void process()
    {
        if (m_cancelled) {
            emit finished();
            return;
        }
        
        HarmonyResult result;
        result.config = m_config;
        
        // 生成和谐色彩
        switch (m_config.type) {
            case HarmonyType::Complementary:
                result.colors = m_parent->generateComplementary(m_baseColor, m_config);
                break;
            case HarmonyType::Analogous:
                result.colors = m_parent->generateAnalogous(m_baseColor, m_config);
                break;
            case HarmonyType::Triadic:
                result.colors = m_parent->generateTriadic(m_baseColor, m_config);
                break;
            case HarmonyType::Tetradic:
                result.colors = m_parent->generateTetradic(m_baseColor, m_config);
                break;
            case HarmonyType::SplitComplementary:
                result.colors = m_parent->generateSplitComplementary(m_baseColor, m_config);
                break;
            case HarmonyType::Monochromatic:
                result.colors = m_parent->generateMonochromatic(m_baseColor, m_config);
                break;
            case HarmonyType::Square:
                result.colors = m_parent->generateSquare(m_baseColor, m_config);
                break;
            case HarmonyType::DoubleSplitComplementary:
                result.colors = m_parent->generateDoubleSplitComplementary(m_baseColor, m_config);
                break;
            default:
                result.colors = {m_baseColor};
                break;
        }
        
        if (m_cancelled) {
            emit finished();
            return;
        }
        
        // 应用色域限制
        result.colors = m_parent->filterByGamut(result.colors, m_config.gamut);
        
        // 计算和谐度评分
        result.overallScore = m_parent->calculateHarmonyScore(result.colors, m_config.type);
        
        // 生成颜色名称
        result.colorNames = m_parent->generateColorNames(result.colors);
        
        // 设置描述
        result.description = HARMONY_DESCRIPTIONS.value(m_config.type, "Unknown harmony type");
        
        emit harmonyGenerated(result);
        emit finished();
    }

signals:
    void harmonyGenerated(const HarmonyResult& result);
    void finished();

private:
    HarmonyEngine* m_parent;
    QColor m_baseColor;
    HarmonyConfig m_config;
    bool m_cancelled;
};

// HarmonyEngine实现
HarmonyEngine::HarmonyEngine(QObject *parent)
    : QObject(parent)
    , m_worker(std::make_unique<HarmonyWorker>(this))
    , m_isGenerating(false)
{
    // 设置默认配置
    m_defaultConfig.type = HarmonyType::Complementary;
    m_defaultConfig.colorSpace = ColorSpace::HSV;
    m_defaultConfig.lockLightness = false;
    m_defaultConfig.lockSaturation = false;
    m_defaultConfig.lightnessVariation = 20.0;
    m_defaultConfig.saturationVariation = 20.0;
    m_defaultConfig.hueShift = 0.0;
    m_defaultConfig.colorCount = 5;
    m_defaultConfig.gamut = ColorGamut::sRGB;
}

HarmonyEngine::~HarmonyEngine() = default;

// 生成和谐色彩
HarmonyResult HarmonyEngine::generateHarmony(const QColor& baseColor, const HarmonyConfig& config)
{
    // 检查缓存
    QString cacheKey = QString("%1_%2_%3_%4_%5")
        .arg(baseColor.name())
        .arg(static_cast<int>(config.type))
        .arg(static_cast<int>(config.colorSpace))
        .arg(config.colorCount)
        .arg(config.hueShift);
    
    if (m_harmonyCache.contains(cacheKey)) {
        return m_harmonyCache[cacheKey];
    }
    
    HarmonyResult result;
    result.config = config;
    
    // 生成基础和谐色彩
    switch (config.type) {
        case HarmonyType::Complementary:
            result.colors = generateComplementary(baseColor, config);
            break;
        case HarmonyType::Analogous:
            result.colors = generateAnalogous(baseColor, config);
            break;
        case HarmonyType::Triadic:
            result.colors = generateTriadic(baseColor, config);
            break;
        case HarmonyType::Tetradic:
            result.colors = generateTetradic(baseColor, config);
            break;
        case HarmonyType::SplitComplementary:
            result.colors = generateSplitComplementary(baseColor, config);
            break;
        case HarmonyType::Monochromatic:
            result.colors = generateMonochromatic(baseColor, config);
            break;
        case HarmonyType::Square:
            result.colors = generateSquare(baseColor, config);
            break;
        case HarmonyType::DoubleSplitComplementary:
            result.colors = generateDoubleSplitComplementary(baseColor, config);
            break;
        default:
            result.colors = {baseColor};
            break;
    }
    
    // 应用变化和限制
    result.colors = applyVariations(result.colors, config);
    result.colors = filterByGamut(result.colors, config.gamut);
    
    // 计算评分
    result.overallScore = calculateHarmonyScore(result.colors, config.type);
    
    // 生成名称和描述
    result.colorNames = generateColorNames(result.colors);
    result.description = HARMONY_DESCRIPTIONS.value(config.type, "Unknown harmony type");
    
    // 缓存结果
    if (m_harmonyCache.size() >= MAX_CACHE_SIZE) {
        m_harmonyCache.clear();
    }
    m_harmonyCache[cacheKey] = result;
    
    return result;
}

// 生成互补色
QVector<QColor> HarmonyEngine::generateComplementary(const QColor& base, const HarmonyConfig& config)
{
    QVector<QColor> colors;
    colors.append(base);
    
    // 获取互补色 (色相偏移180度)
    QColor complement = adjustColorInSpace(base, 180.0 + config.hueShift, config);
    colors.append(complement);
    
    // 如果需要更多颜色，添加中间色调
    while (colors.size() < config.colorCount) {
        double hueStep = 180.0 / (config.colorCount - 1);
        double hue = hueStep * (colors.size() - 1);
        colors.append(adjustColorInSpace(base, hue + config.hueShift, config));
    }
    
    return colors;
}

// 生成邻近色
QVector<QColor> HarmonyEngine::generateAnalogous(const QColor& base, const HarmonyConfig& config)
{
    QVector<QColor> colors;
    colors.append(base);
    
    // 邻近色通常在±30度范围内
    double angleStep = 60.0 / (config.colorCount - 1);
    
    for (int i = 1; i < config.colorCount; ++i) {
        double hueOffset = -30.0 + angleStep * i + config.hueShift;
        colors.append(adjustColorInSpace(base, hueOffset, config));
    }
    
    return colors;
}

// 生成三角色
QVector<QColor> HarmonyEngine::generateTriadic(const QColor& base, const HarmonyConfig& config)
{
    QVector<QColor> colors;
    colors.append(base);
    
    // 三角色：120度间隔
    colors.append(adjustColorInSpace(base, 120.0 + config.hueShift, config));
    colors.append(adjustColorInSpace(base, 240.0 + config.hueShift, config));
    
    // 如果需要更多颜色，在三角形内部添加
    while (colors.size() < config.colorCount) {
        double hue = 120.0 * ((colors.size() - 1) % 3) + config.hueShift;
        colors.append(adjustColorInSpace(base, hue, config));
    }
    
    return colors;
}

// 生成四角色
QVector<QColor> HarmonyEngine::generateTetradic(const QColor& base, const HarmonyConfig& config)
{
    QVector<QColor> colors;
    colors.append(base);
    
    // 四角色：两对互补色
    colors.append(adjustColorInSpace(base, 60.0 + config.hueShift, config));
    colors.append(adjustColorInSpace(base, 180.0 + config.hueShift, config));
    colors.append(adjustColorInSpace(base, 240.0 + config.hueShift, config));
    
    // 如果需要更多颜色，重复模式
    while (colors.size() < config.colorCount) {
        int index = (colors.size() - 1) % 4;
        double hue = index * 60.0 + (index >= 2 ? 120.0 : 0.0) + config.hueShift;
        colors.append(adjustColorInSpace(base, hue, config));
    }
    
    return colors;
}

#include "HarmonyEngine.moc"
