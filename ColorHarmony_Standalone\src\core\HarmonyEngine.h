#pragma once

#include <QObject>
#include <QColor>
#include <QVector>
#include <QMap>
#include <QString>
#include <QJsonObject>
#include <QJsonArray>
#include <memory>

#include "ColorConverter.h"

// 和谐类型枚举
enum class HarmonyType {
    Complementary = 0,      // 互补色
    Analogous = 1,          // 邻近色
    Triadic = 2,            // 三角色
    Tetradic = 3,           // 四角色 (矩形)
    SplitComplementary = 4, // 分裂互补色
    Monochromatic = 5,      // 单色调
    Square = 6,             // 正方形
    DoubleSplitComplementary = 7, // 双分裂互补色
    Custom = 8              // 自定义
};

// 和谐配置结构
struct HarmonyConfig {
    HarmonyType type;
    ColorSpace colorSpace;
    bool lockLightness;
    bool lockSaturation;
    double lightnessVariation;  // 亮度变化范围 (0-100)
    double saturationVariation; // 饱和度变化范围 (0-100)
    double hueShift;           // 色相偏移 (0-360)
    int colorCount;            // 生成颜色数量
    ColorGamut gamut;          // 色域限制
    
    HarmonyConfig() 
        : type(HarmonyType::Complementary)
        , colorSpace(ColorSpace::HSV)
        , lockLightness(false)
        , lockSaturation(false)
        , lightnessVariation(20.0)
        , saturationVariation(20.0)
        , hueShift(0.0)
        , colorCount(5)
        , gamut(ColorGamut::sRGB)
    {}
};

// 颜色和谐结果
struct HarmonyResult {
    QVector<QColor> colors;
    QVector<QString> colorNames;
    QVector<double> harmonyScores;  // 和谐度评分 (0-1)
    double overallScore;            // 整体和谐度
    QString description;            // 和谐描述
    HarmonyConfig config;           // 使用的配置
    
    HarmonyResult() : overallScore(0.0) {}
};

// 颜色和谐引擎
class HarmonyEngine : public QObject
{
    Q_OBJECT
    
public:
    explicit HarmonyEngine(QObject *parent = nullptr);
    ~HarmonyEngine();
    
    // 主要功能
    HarmonyResult generateHarmony(const QColor& baseColor, const HarmonyConfig& config);
    HarmonyResult generateHarmony(const QColor& baseColor, HarmonyType type, ColorSpace space = ColorSpace::HSV);
    
    // 预设和谐方案
    QVector<HarmonyResult> generatePresetHarmonies(const QColor& baseColor);
    HarmonyResult getBestHarmony(const QColor& baseColor, const QVector<QColor>& existingColors = {});
    
    // 和谐分析
    double analyzeHarmony(const QVector<QColor>& colors, HarmonyType type = HarmonyType::Custom);
    QVector<double> analyzeColorRelationships(const QVector<QColor>& colors);
    QString getHarmonyDescription(HarmonyType type);
    
    // 颜色建议
    QVector<QColor> suggestColors(const QColor& baseColor, int count, const HarmonyConfig& config);
    QColor suggestAccentColor(const QVector<QColor>& palette);
    QColor suggestNeutralColor(const QVector<QColor>& palette);
    
    // 配置管理
    void setDefaultConfig(const HarmonyConfig& config);
    HarmonyConfig getDefaultConfig() const;
    void saveConfig(const QString& name, const HarmonyConfig& config);
    HarmonyConfig loadConfig(const QString& name);
    QStringList getConfigNames() const;
    
    // 导入导出
    QJsonObject exportHarmony(const HarmonyResult& result);
    HarmonyResult importHarmony(const QJsonObject& json);
    
    // 实用函数
    static QString harmonyTypeToString(HarmonyType type);
    static HarmonyType stringToHarmonyType(const QString& str);
    static QVector<HarmonyType> getAllHarmonyTypes();

signals:
    void harmonyGenerated(const HarmonyResult& result);
    void configChanged(const HarmonyConfig& config);
    void progressChanged(int percentage);

public slots:
    void generateHarmonyAsync(const QColor& baseColor, const HarmonyConfig& config);
    void cancelGeneration();

private slots:
    void onGenerationFinished();

private:
    // 核心算法
    QVector<QColor> generateComplementary(const QColor& base, const HarmonyConfig& config);
    QVector<QColor> generateAnalogous(const QColor& base, const HarmonyConfig& config);
    QVector<QColor> generateTriadic(const QColor& base, const HarmonyConfig& config);
    QVector<QColor> generateTetradic(const QColor& base, const HarmonyConfig& config);
    QVector<QColor> generateSplitComplementary(const QColor& base, const HarmonyConfig& config);
    QVector<QColor> generateMonochromatic(const QColor& base, const HarmonyConfig& config);
    QVector<QColor> generateSquare(const QColor& base, const HarmonyConfig& config);
    QVector<QColor> generateDoubleSplitComplementary(const QColor& base, const HarmonyConfig& config);
    
    // 辅助函数
    QColor adjustColorInSpace(const QColor& color, double hueOffset, const HarmonyConfig& config);
    QVector<QColor> applyVariations(const QVector<QColor>& baseColors, const HarmonyConfig& config);
    QVector<QColor> filterByGamut(const QVector<QColor>& colors, ColorGamut gamut);
    double calculateHarmonyScore(const QVector<QColor>& colors, HarmonyType type);
    QVector<QString> generateColorNames(const QVector<QColor>& colors);
    
    // 颜色关系分析
    double calculateColorBalance(const QVector<QColor>& colors);
    double calculateColorContrast(const QVector<QColor>& colors);
    double calculateColorTemperature(const QVector<QColor>& colors);
    double calculateColorSaturation(const QVector<QColor>& colors);
    
    // 数据成员
    HarmonyConfig m_defaultConfig;
    QMap<QString, HarmonyConfig> m_savedConfigs;
    
    // 异步处理
    class HarmonyWorker;
    std::unique_ptr<HarmonyWorker> m_worker;
    bool m_isGenerating;
    
    // 缓存
    mutable QMap<QString, HarmonyResult> m_harmonyCache;
    static const int MAX_CACHE_SIZE = 100;
    
    // 常量
    static const QMap<HarmonyType, QString> HARMONY_TYPE_NAMES;
    static const QMap<HarmonyType, QString> HARMONY_DESCRIPTIONS;
    static const QVector<double> GOLDEN_RATIOS;
};
