#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QStandardPaths>
#include <QMessageBox>
#include <QTranslator>
#include <QLocale>
#include <QLibraryInfo>

#include "MainWindow.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 设置应用程序信息
    app.setApplicationName("Color Harmony");
    app.setApplicationVersion("1.0.0");
    app.setApplicationDisplayName("Color Harmony - Professional Color Palette Tool");
    app.setOrganizationName("ColorTools Studio");
    app.setOrganizationDomain("colortools.studio");
    
    // 设置应用程序图标
    app.setWindowIcon(QIcon(":/icons/app.png"));
    
    // 创建应用程序数据目录
    QString appDataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir appDataDir(appDataPath);
    if (!appDataDir.exists()) {
        appDataDir.mkpath(".");
        appDataDir.mkpath("palettes");
        appDataDir.mkpath("themes");
        appDataDir.mkpath("exports");
    }
    
    // 设置现代化样式 - Windows 11风格
    app.setStyle(QStyleFactory::create("Fusion"));

    // 应用Windows 11风格的现代暗色主题
    QPalette modernPalette;
    // Windows 11 暗色主题颜色
    modernPalette.setColor(QPalette::Window, QColor(32, 32, 32));          // 主背景
    modernPalette.setColor(QPalette::WindowText, QColor(255, 255, 255));   // 主文本
    modernPalette.setColor(QPalette::Base, QColor(45, 45, 45));            // 输入框背景
    modernPalette.setColor(QPalette::AlternateBase, QColor(60, 60, 60));    // 交替背景
    modernPalette.setColor(QPalette::ToolTipBase, QColor(70, 70, 70));      // 工具提示背景
    modernPalette.setColor(QPalette::ToolTipText, QColor(255, 255, 255));   // 工具提示文本
    modernPalette.setColor(QPalette::Text, QColor(255, 255, 255));          // 普通文本
    modernPalette.setColor(QPalette::Button, QColor(60, 60, 60));           // 按钮背景
    modernPalette.setColor(QPalette::ButtonText, QColor(255, 255, 255));    // 按钮文本
    modernPalette.setColor(QPalette::BrightText, QColor(255, 80, 80));      // 高亮文本
    modernPalette.setColor(QPalette::Link, QColor(99, 162, 255));           // 链接颜色 (Windows 11 蓝)
    modernPalette.setColor(QPalette::Highlight, QColor(0, 120, 215));       // 选中背景 (Windows 11 蓝)
    modernPalette.setColor(QPalette::HighlightedText, QColor(255, 255, 255)); // 选中文本
    modernPalette.setColor(QPalette::PlaceholderText, QColor(150, 150, 150)); // 占位符文本
    app.setPalette(modernPalette);
    
    // 设置Windows 11风格的现代扁平化样式表
    app.setStyleSheet(R"(
        /* 全局字体设置 */
        * {
            font-family: "Segoe UI Variable", "Segoe UI", system-ui, sans-serif;
        }

        /* 工具提示 - Windows 11风格 */
        QToolTip {
            color: #ffffff;
            background-color: rgba(45, 45, 45, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 13px;
        }

        /* 菜单栏 - 扁平化设计 */
        QMenuBar {
            background-color: #202020;
            color: #ffffff;
            border: none;
            padding: 4px;
        }

        QMenuBar::item {
            background: transparent;
            padding: 8px 16px;
            border-radius: 6px;
            margin: 2px;
        }

        QMenuBar::item:selected,
        QMenuBar::item:pressed {
            background-color: rgba(255, 255, 255, 0.1);
        }

        /* 菜单 - 现代化下拉菜单 */
        QMenu {
            background-color: #2d2d2d;
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 8px;
        }

        QMenu::item {
            padding: 8px 16px;
            border-radius: 6px;
            margin: 1px;
        }

        QMenu::item:selected {
            background-color: #0078d4;
        }

        QMenu::separator {
            height: 1px;
            background-color: rgba(255, 255, 255, 0.1);
            margin: 4px 8px;
        }

        /* 状态栏 */
        QStatusBar {
            background-color: #202020;
            color: #ffffff;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* 分组框 - 现代化边框 */
        QGroupBox {
            font-weight: 600;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            margin-top: 12px;
            padding-top: 16px;
            background-color: rgba(255, 255, 255, 0.02);
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 12px;
            padding: 0 8px;
            color: #ffffff;
            font-size: 14px;
        }

        /* 按钮 - Windows 11风格 */
        QPushButton {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 500;
            min-height: 20px;
        }

        QPushButton:hover {
            background-color: #484848;
            border-color: rgba(255, 255, 255, 0.2);
        }

        QPushButton:pressed {
            background-color: #2a2a2a;
        }

        QPushButton:disabled {
            background-color: #2a2a2a;
            color: #666666;
            border-color: rgba(255, 255, 255, 0.05);
        }

        /* 主要按钮 */
        QPushButton[primary="true"] {
            background-color: #0078d4;
            border-color: #0078d4;
        }

        QPushButton[primary="true"]:hover {
            background-color: #106ebe;
        }

        QPushButton[primary="true"]:pressed {
            background-color: #005a9e;
        }

        /* 输入框 - 现代化输入框 */
        QLineEdit, QTextEdit, QPlainTextEdit {
            background-color: #2d2d2d;
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            padding: 8px 12px;
            selection-background-color: #0078d4;
        }

        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border-color: #0078d4;
            background-color: #323232;
        }

        /* 下拉框 - 现代化下拉框 */
        QComboBox {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            padding: 8px 12px;
            min-width: 120px;
        }

        QComboBox:hover {
            background-color: #484848;
            border-color: rgba(255, 255, 255, 0.2);
        }

        QComboBox::drop-down {
            border: none;
            width: 30px;
        }

        QComboBox::down-arrow {
            image: url(:/icons/chevron-down.svg);
            width: 12px;
            height: 12px;
        }

        QComboBox QAbstractItemView {
            background-color: #2d2d2d;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            selection-background-color: #0078d4;
            outline: none;
        }

        /* 滑块 - 现代化滑块 */
        QSlider::groove:horizontal {
            background-color: #3c3c3c;
            height: 6px;
            border-radius: 3px;
        }

        QSlider::handle:horizontal {
            background-color: #0078d4;
            border: 2px solid #ffffff;
            width: 20px;
            height: 20px;
            border-radius: 12px;
            margin: -8px 0;
        }

        QSlider::handle:horizontal:hover {
            background-color: #106ebe;
        }

        QSlider::handle:horizontal:pressed {
            background-color: #005a9e;
        }

        /* 滚动条 - 现代化滚动条 */
        QScrollBar:vertical {
            background: transparent;
            width: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:vertical {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            min-height: 30px;
            margin: 2px;
        }

        QScrollBar::handle:vertical:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        QScrollBar::add-line:vertical,
        QScrollBar::sub-line:vertical {
            border: none;
            background: none;
            height: 0px;
        }

        QScrollBar:horizontal {
            background: transparent;
            height: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:horizontal {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            min-width: 30px;
            margin: 2px;
        }

        QScrollBar::handle:horizontal:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* 标签页 - 现代化标签页 */
        QTabWidget::pane {
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            background-color: #2d2d2d;
        }

        QTabBar::tab {
            background-color: transparent;
            color: #cccccc;
            padding: 12px 20px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            margin-right: 2px;
        }

        QTabBar::tab:selected {
            background-color: #2d2d2d;
            color: #ffffff;
            border-bottom: 2px solid #0078d4;
        }

        QTabBar::tab:hover:!selected {
            background-color: rgba(255, 255, 255, 0.05);
            color: #ffffff;
        }

        /* 复选框和单选框 - 现代化设计 */
        QCheckBox, QRadioButton {
            color: #ffffff;
            spacing: 8px;
        }

        QCheckBox::indicator, QRadioButton::indicator {
            width: 18px;
            height: 18px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            background-color: transparent;
        }

        QCheckBox::indicator {
            border-radius: 4px;
        }

        QRadioButton::indicator {
            border-radius: 10px;
        }

        QCheckBox::indicator:checked, QRadioButton::indicator:checked {
            background-color: #0078d4;
            border-color: #0078d4;
        }

        QCheckBox::indicator:hover, QRadioButton::indicator:hover {
            border-color: rgba(255, 255, 255, 0.5);
        }
    )");
    
    // 国际化支持
    QTranslator translator;
    const QStringList uiLanguages = QLocale::system().uiLanguages();
    for (const QString &locale : uiLanguages) {
        const QString baseName = "ColorHarmony_" + QLocale(locale).name();
        if (translator.load(":/i18n/" + baseName)) {
            app.installTranslator(&translator);
            break;
        }
    }
    
    // 创建并显示主窗口
    try {
        MainWindow window;
        window.show();
        
        return app.exec();
    }
    catch (const std::exception& e) {
        QMessageBox::critical(nullptr, "Error", 
            QString("Failed to start Color Harmony: %1").arg(e.what()));
        return -1;
    }
}
