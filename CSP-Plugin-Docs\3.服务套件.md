服务套件提供了可在插件模块端使用的功能。服务套件函数返回两个 TriglavPlugInInt 类型的常量。 返回 kTriglavPlugInAPIResultSuccess 表示成功，返回 kTriglavPlugInAPIResultFailed 表示操作失败。
先看看插件服务器跟各个服务套件是如何关联起来的：

```C++
// 插件服务器
typedef struct _TriglavPlugInServer {
    TriglavPlugInRecordSuite        recordSuite;
    TriglavPlugInServiceSuite       serviceSuite;
    TriglavPlugInHostObject         hostObject;
} TriglavPlugInServer;
// 插件服务套件
typedef struct _TriglavPlugInServiceSuite {
    TriglavPlugInStringService*        stringService;
    TriglavPlugInBitmapService*        bitmapService;
    TriglavPlugInOffscreenService*     offscreenService;
    TriglavPlugInPropertyService*      propertyService;
    TriglavPlugInOffscreenService2*    offscreenService2;
    TriglavPlugInPropertyService2*     propertyService2;
    ...
} TriglavPlugInServiceSuite;
```

本篇介绍字符串服务和位图服务。

# 一、字符串服务

该服务主要提供处理字符串的功能。结构体定义如下：
```C++
typedef struct _TriglavPlugInStringService {
    TriglavPlugInStringCreateWithAsciiStringProc        createWithAsciiStringProc;
    TriglavPlugInStringCreateWithUnicodeStringProc      createWithUnicodeStringProc;
    TriglavPlugInStringCreateWithLocalCodeStringProc    createWithLocalCodeStringProc;
    TriglavPlugInStringCreateWithStringIDProc           createWithStringIDProc;
    TriglavPlugInStringRetainProc                       retainProc;
    TriglavPlugInStringReleaseProc                      releaseProc;
    TriglavPlugInStringGetUnicodeCharsProc              getUnicodeCharsProc;
    TriglavPlugInStringGetUnicodeLengthProc             getUnicodeLengthProc;
    TriglavPlugInStringGetLocalCodeCharsProc            getLocalCodeCharsProc;
    TriglavPlugInStringGetLocalCodeLengthProc           getLocalCodeLengthProc;
} TriglavPlugInStringService;
```
1. 从给定的 **ascii** 字符串创建一个字符串对象，不需要时需手动调用函数 **releaceProc**() 销毁。
```C++
/**
 * stringObject: 字符串对象
 * ascii: ascii 字符串
 * length: ascii 字符串长度
**/
TRIGLAV_PLUGIN_API *createWithAsciiStringProc(
    TriglavPlugInStringObject* stringObject, 
    const TriglavPlugInChar* ascii, 
    const TriglavPlugInInt length);
```
  2. 从给定的 unicode 字符串创建一个字符串对象，不需要时需手动调用函数 releaceProc() 销毁。
```C++
/**
 * stringObject: 字符串对象
 * unicode: unicode 字符串
 * length: unicode 字符串长度
**/
TRIGLAV_PLUGIN_API *createWithUnicodeStringProc( 
    TriglavPlugInStringObject* stringObject, 
    const TriglavPlugInUniChar* unicode, 
    const TriglavPlugInInt length );
```
 3. 从指定的本地代码字符串创建字符串对象，不需要时需手动调用函数 **releaceProc**() 销毁。
```C++
/**
 * stringObject: 字符串对象
 * localcode: 本地代码字符串
 * length: 本地代码字符串长度
**/
TRIGLAV_PLUGIN_API *createWithLocalCodeStringProc( 
    TriglavPlugInStringObject* stringObject, 
    const TriglavPlugInChar* localcode, 
    const TriglavPlugInInt length);
```
4. 根据指定的资源标识符创建字符串对象，不需要时需手动调用函数 **releaceProc**() 销毁。
```C++
/**
 * stringObject: 字符串对象
 * stringID: 资源标识符
 * hostObject: 宿主对象
**/
TRIGLAV_PLUGIN_API *createWithStringIDProc( 
    TriglavPlugInStringObject* stringObject, 
    const TriglavPlugInInt stringID, 
    TriglavPlugInHostObject hostObject);
```

这里的 **stringID** 获取需要注意平台：
	**windows**: 请使用 Visula Studio 的 String Table。将这里设置的值代入自变量 **stringID** 中，就会生成与之对应的字符串对象；
	**mac**: 请使用 Localizable.strings。将这里设置的值代入自变量 **stringID** 中，就会生成与之对应的字符串对象。
	
5. 销毁指定的字符串对象
```C++
/**
 * stringObject: 字符串对象
**/
TRIGLAV_PLUGIN_API *releaseProc(
    TriglavPlugInStringObject stringObject);
```
6. 从指定的字符串对象获取 **unicode** 字符串
```C++
/**
 * unicode: unicode 字符串
 * stringObject: 字符串对象
**/
TRIGLAV_PLUGIN_API *getUnicodeCharsProc( 
    const TriglavPlugInUniChar** unicode, 
    TriglavPlugInStringObject stringObject);
```

7. 从指定的字符串对象获取 **unicode** 字符串长度

```c++
/**
 * length: unicode 字符串长度
 * stringObject: 字符串对象
**/
TRIGLAV_PLUGIN_API *getUnicodeLengthProc( 
    TriglavPlugInInt* length, 
    TriglavPlugInStringObject stringObject);
```

8. 从指定的字符串对象获取本地代码字符串

```c++
/**
 * localcode: 本地代码字符串
 * stringObject: 字符串对象
**/
TRIGLAV_PLUGIN_API *getLocalCodeCharsProc( 
    const TriglavPlugInUniChar** localcode, 
    TriglavPlugInStringObject stringObject );

```

9. 从指定的字符串对象获取本地代码字符串长度

```c++
/**
 * length: 本地代码字符串长度
 * stringObject: 字符串对象
**/
TRIGLAV_PLUGIN_API *getLocalCodeLengthProc( 
    TriglavPlugInInt* length, 
    TriglavPlugInStringObject stringObject );
```

# 二、位图服务

​    该服务主要提供使用位图的功能。结构体定义如下：

```c++
typedef struct _TriglavPlugInBitmapService {
    TriglavPlugInBitmapCreateProc               createProc;
    TriglavPlugInBitmapRetainProc               retainProc;
    TriglavPlugInBitmapReleaseProc              releaseProc;
    TriglavPlugInBitmapGetWidthProc             getWidthProc;
    TriglavPlugInBitmapGetHeightProc            getHeightProc;
    TriglavPlugInBitmapGetDepthProc             getDepthProc;
    TriglavPlugInBitmapGetScanlineProc          getScanlineProc;
    TriglavPlugInBitmapGetAddressProc           getAddressProc;
    TriglavPlugInBitmapGetRowBytesProc          getRowBytesProc;
    TriglavPlugInBitmapGetPixelBytesProc        getPixelBytesProc;
} TriglavPlugInBitmapService;
```

1. 创建位图对象，不需要时需手动调用函数 **releaceProc**() 销毁

```c++
/**
 * bitmapObject: 位图对象
 * width: 宽度（像素）
 * height: 高度（像素）
 * depth: 颜色深度（字节）
 * scanline: 扫描线
**/
TRIGLAV_PLUGIN_API *createProc( 
    TriglavPlugInBitmapObject* bitmapObject, 
    const TriglavPlugInInt width, 
    const TriglavPlugInInt height, 
    const TriglavPlugInInt depth, 
    const TriglavPlugInInt scanline );
```

**scanline** 可选常量如下：

```c++
#define    kTriglavPlugInBitmapScanlineHorizontalLeftTop        (0x10)
#define    kTriglavPlugInBitmapScanlineHorizontalRightTop       (0x11)
#define    kTriglavPlugInBitmapScanlineHorizontalLeftBottom     (0x12)
#define    kTriglavPlugInBitmapScanlineHorizontalRightBottom    (0x13)
#define    kTriglavPlugInBitmapScanlineVerticalLeftTop          (0x14)
#define    kTriglavPlugInBitmapScanlineVerticalRightTop         (0x15)
#define    kTriglavPlugInBitmapScanlineVerticalLeftBottom       (0x16)
#define    kTriglavPlugInBitmapScanlineVerticalRightBottom      (0x17)
```

2. 销毁指定的 **bitmap** 对象

```c++

/**
 * bitmapObject: 位图对象
**/
TRIGLAV_PLUGIN_API *releaseProc( 
    TriglavPlugInBitmapObject bitmapObject );
```

3. 获取指定 **bitmap** 对象的宽度

```c++
/**
 * bitmapObject: 位图对象
 * width: 宽度（像素）
**/
TRIGLAV_PLUGIN_API *getWidthProc( 
    TriglavPlugInBitmapObject bitmapObject, 
    TriglavPlugInInt* width );
```

4. 获取指定 **bitmap** 对象的高度

```c++
/**
 * bitmapObject: 位图对象
 * height: 高度（像素）
**/
TRIGLAV_PLUGIN_API *getHeightProc( 
    TriglavPlugInBitmapObject bitmapObject, 
    TriglavPlugInInt* height );
```

5. 获取指定 **bitmap** 对象的颜色深度

```c++
/**
 * bitmapObject: 位图对象
 * depth: 颜色深度（字节数）
**/
TRIGLAV_PLUGIN_API *getDepthProc( 
    TriglavPlugInBitmapObject bitmapObject, 
    TriglavPlugInInt* depth );
```

6. 获取指定 **bitmap** 对象的扫描线

```c++
/**
 * bitmapObject: 位图对象
 * scanline: 扫描线
**/
TRIGLAV_PLUGIN_API *getScanlineProc( 
    TriglavPlugInBitmapObject bitmapObject, 
    TriglavPlugInInt* scanline );
```

7. 获取指定 **bitmap** 对象的参数 **pos** 的坐标地址

```c++
/**
 * bitmapObject: 位图对象
 * pos: 坐标
 * address: 地址
**/
TRIGLAV_PLUGIN_API *getAddressProc( 
    TriglavPlugInBitmapObject bitmapObject, 
    const TriglavPlugInPoint* pos, 
    TriglavPlugInPtr* address );
```

8. 获取指定 **bitmap** 对象的下一列像素的字节数

```c++
/**
 * bitmapObject: 位图对象
 * rowBytes: 到下一列像素的字节数
**/
TRIGLAV_PLUGIN_API *getRowBytesProc( 
    TriglavPlugInBitmapObject bitmapObject, 
    TriglavPlugInInt* rowBytes );
```

9. 获取指定 **bitmap** 对象的下一行像素的字节数

```c++
/**
 * bitmapObject: 位图对象
 * pixelBytes: 到下一行像素的字节数
**/
TRIGLAV_PLUGIN_API *getPixelBytesProc( 
    TriglavPlugInBitmapObject bitmapObject, 
    TriglavPlugInInt* pixelBytes );
```

# 三、离屏服务

该服务主要提供处理离屏的功能。[结构体定义](https://so.csdn.net/so/search?q=结构体定义&spm=1001.2101.3001.7020)如下：

```c++
typedef struct _TriglavPlugInOffscreenService {
    TriglavPlugInOffscreenCreatePlaneProc            createPlaneProc;
    TriglavPlugInOffscreenRetainProc                 retainProc;
    TriglavPlugInOffscreenReleaseProc                releaseProc;
    TriglavPlugInOffscreenGetWidthProc               getWidthProc;
    TriglavPlugInOffscreenGetHeightProc              getHeightProc;
    TriglavPlugInOffscreenGetRectProc                getRectProc;
    TriglavPlugInOffscreenGetExtentRectProc          getExtentRectProc;
    TriglavPlugInOffscreenGetChannelOrderProc        getChannelOrderProc;
    TriglavPlugInOffscreenGetRGBChannelIndexProc     getRGBChannelIndexProc;
    TriglavPlugInOffscreenGetCMYKChannelIndexProc    getCMYKChannelIndexProc;
    TriglavPlugInOffscreenGetBlockRectCountProc      getBlockRectCountProc;
    TriglavPlugInOffscreenGetBlockRectProc           getBlockRectProc;
    TriglavPlugInOffscreenGetBlockImageProc          getBlockImageProc;
    TriglavPlugInOffscreenGetBlockAlphaProc          getBlockAlphaProc;
    TriglavPlugInOffscreenGetBlockSelectAreaProc     getBlockSelectAreaProc;
    TriglavPlugInOffscreenGetBlockPlaneProc          getBlockPlaneProc;
    TriglavPlugInOffscreenGetTileWidthProc           getTileWidthProc;
    TriglavPlugInOffscreenGetTileHeightProc          getTileHeightProc;
    TriglavPlugInOffscreenGetBitmapProc              getBitmapProc;
    TriglavPlugInOffscreenSetBitmapProc              setBitmapProc;
} TriglavPlugInOffscreenService;
```

1. 为指定的离屏对象创建一个空的离屏，不需要时需手动调用函数 **releaceProc**() 销毁。

```c++
/**
 * offscreenObject: 离屏对象
 * width: 宽度（像素）
 * height: 高度（像素）
 * depth: 颜色深度（字节）
**/
TRIGLAV_PLUGIN_API *createPlaneProc( 
    TriglavPlugInOffscreenObject* offscreenObject, 
    const TriglavPlugInInt width, 
    const TriglavPlugInInt height, 
    const TriglavPlugInInt depth );
```

2. 销毁指定的离屏对象

```c++
/**
 * offscreenObject: 离屏对象
**/
TRIGLAV_PLUGIN_API *releaseProc( 
    TriglavPlugInOffscreenObject offscreenObject );
```

3. 获取指定离屏对象的宽度

```c++
/**
 * offscreenObject: 离屏对象
 * width: 宽度（像素）
**/
TRIGLAV_PLUGIN_API *getWidthProc( 
    TriglavPlugInOffscreenObject offscreenObject, 
    TriglavPlugInInt* width );
```

4. 获取指定离屏对象的高度

```c++
/**
 * offscreenObject: 离屏对象
 * height: 高度（像素）
**/
TRIGLAV_PLUGIN_API *getHeightProc( 
    TriglavPlugInOffscreenObject offscreenObject, 
    TriglavPlugInInt* height );
```

5. 获取指定离屏对象的大小

```c++
/**
 * offscreenObject: 离屏对象
 * rect: 离屏尺寸（像素）
**/
TRIGLAV_PLUGIN_API *getRectProc( 
    TriglavPlugInOffscreenObject offscreenObject, 
    TriglavPlugInRect* rect );
```

6. 获取指定离屏对象的绘制区域的外接矩形

```c++
/**
 * offscreenObject: 离屏对象
 * rect: 绘制区域的外接矩形（像素）
**/
TRIGLAV_PLUGIN_API *getExtentRectProc( 
    TriglavPlugInOffscreenObject offscreenObject, 
    TriglavPlugInRect* rect );
```

7. 获取指定离屏对象的通道对齐信息

```c++
/**
 * offscreenObject: 离屏对象
 * channelOrder: 通道对齐信息
**/
TRIGLAV_PLUGIN_API *getChannelOrderProc( 
    TriglavPlugInOffscreenObject offscreenObject, 
    TriglavPlugInInt* channelOrder );
```

获取的 **channel order** 为下述常量之一：

```c++
#define    kTriglavPlugInOffscreenChannelOrderAlpha                     (0x01)
#define    kTriglavPlugInOffscreenChannelOrderGrayAlpha                 (0x02)
#define    kTriglavPlugInOffscreenChannelOrderRGBAlpha                  (0x03)
#define    kTriglavPlugInOffscreenChannelOrderCMYKAlpha                 (0x04)
#define    kTriglavPlugInOffscreenChannelOrderBinarizationAlpha         (0x05)
#define    kTriglavPlugInOffscreenChannelOrderBinarizationGrayAlpha     (0x06)
#define    kTriglavPlugInOffscreenChannelOrderSelectArea                (0x10)
#define    kTriglavPlugInOffscreenChannelOrderPlane                     (0x20)
```

8. 获取指定离屏对象的 **RGB channel index**

```c++
/**
 * offscreenObject: 离屏对象
 * redChannelIndex: 红色 channel index
 * blueChannelIndex: 蓝色 channel index
 * greenChannelIndex: 绿色 channel index
**/
TRIGLAV_PLUGIN_API *getRGBChannelIndexProc( 
    TriglavPlugInOffscreenObject offscreenObject, 
    TriglavPlugInInt* redChannelIndex, 
    TriglavPlugInInt* blueChannelIndex, 
    TriglavPlugInInt* greenChannelIndex );
```

9. 获取指定离屏对象的 **CMYK channel index**

```c++
/**
 * offscreenObject: 离屏对象
 * cyanChannelIndex: 青色 channel index
 * magentaChannelIndex: 品红色 channel index
 * yellowChannelIndex: 黄色 channel index
 * keytoneChannelIndex: 基调 channel index
**/
TRIGLAV_PLUGIN_API *getCMYKChannelIndexProc( 
    TriglavPlugInOffscreenObject offscreenObject, 
    TriglavPlugInInt* cyanChannelIndex, 
    TriglavPlugInInt* magentaChannelIndex, 
    TriglavPlugInInt* yellowChannelIndex, 
    TriglavPlugInInt* keytoneChannelIndex );
```

10. 获取指定区域的指定离屏对象的块数

```cpp
/**
 * offscreenObject: 离屏对象
 * bounds: 搜索区域
 * blockRectCount: 块数
**/
TRIGLAV_PLUGIN_API *getBlockRectCountProc( 
    TriglavPlugInOffscreenObject offscreenObject, 
    TriglavPlugInRect* bounds, 
    TriglavPlugInInt* blockRectCount);
```

11. 在指定区域中获取指定离屏对象块的区域

```cpp
/**
 * offscreenObject: 离屏对象
 * bounds: 搜索区域
 * blockIndex: 块索引
 * blockRect: 块所在区域
**/
TRIGLAV_PLUGIN_API *getBlockRectProc( 
    TriglavPlugInOffscreenObject offscreenObject, 
    TriglavPlugInRect* bounds, 
    TriglavPlugInInt blockIndex, 
    TriglavPlugInRect* blockRect);
```

12. 在指定的离屏对象中根据指定坐标获取图像地址、到下一列像素的字节数、到下一行像素的字节数以及块的大小

```cpp
/**
 * offscreenObject: 离屏对象
 * pos: 坐标
 * address: 地址
 * rowBytes: 到下一列像素的字节数
 * pixelBytes: 到下一行像素的字节数
 * blockRect: 块所在区域
**/
TRIGLAV_PLUGIN_API *getBlockImageProc( 
    TriglavPlugInOffscreenObject offscreenObject, 
    TriglavPlugInPoint* pos, 
    TriglavPlugInPtr* address, 
    TriglavPlugInInt* rowBytes, 
    TriglavPlugInInt* pixelBytes, 
    TriglavPlugInRect* blockRect);
```

13. 在指定的离屏对象中根据指定坐标获取 **alpha** 地址、到下一列像素的字节数、到下一行像素的字节数以及块的大小

```cpp
/**
 * offscreenObject: 离屏对象
 * pos: 坐标
 * address: 地址
 * rowBytes: 到下一列像素的字节数
 * pixelBytes: 到下一行像素的字节数
 * blockRect: 块所在区域
**/
TRIGLAV_PLUGIN_API *getBlockAlphaProc( 
    TriglavPlugInOffscreenObject offscreenObject, 
    TriglavPlugInPoint* pos, 
    TriglavPlugInPtr* address, 
    TriglavPlugInInt* rowBytes, 
    TriglavPlugInInt* pixelBytes, 
    TriglavPlugInRect* blockRect);
```

14. 在指定的离屏对象中根据指定坐标获取选择区域地址、到下一列像素的字节数、到下一行像素的字节数以及块的大小

```cpp
/**
 * offscreenObject: 离屏对象
 * pos: 坐标
 * address: 地址
 * rowBytes: 到下一列像素的字节数
 * pixelBytes: 到下一行像素的字节数
 * blockRect: 块所在区域
**/
TRIGLAV_PLUGIN_API *getBlockSelectAreaProc( 
    TriglavPlugInOffscreenObject offscreenObject, 
    TriglavPlugInPoint* pos, 
    TriglavPlugInPtr* address, 
    TriglavPlugInInt* rowBytes, 
    TriglavPlugInInt* pixelBytes, 
    TriglavPlugInRect* blockRect);
```

15. 在指定的离屏对象中根据指定坐标获取屏幕地址、到下一列像素的字节数、到下一行像素的字节数以及块的大小

```cpp
/**
 * offscreenObject: 离屏对象
 * pos: 坐标
 * address: 地址
 * rowBytes: 到下一列像素的字节数
 * pixelBytes: 到下一行像素的字节数
 * blockRect: 块所在区域
**/
TRIGLAV_PLUGIN_API *getBlockPlaneProc(
    TriglavPlugInOffscreenObject offscreenObject, 
    TriglavPlugInPoint* pos, 
    TriglavPlugInPtr* address, 
    TriglavPlugInInt* rowBytes, 
    TriglavPlugInInt* pixelBytes, 
    TriglavPlugInRect* blockRect);
```

16. 获取指定离屏对象的主机设置平铺宽度

```cpp
/**
 * offscreenObject: 离屏对象
 * tileWidth: 平铺宽度
**/
TRIGLAV_PLUGIN_API *getTileWidthProc(
    TriglavPlugInOffscreenObject offscreenObject, 
    TriglavPlugInInt* tileWidth);
```

17. 获取指定离屏对象的主机设置平铺高度

```cpp
/**
 * offscreenObject: 离屏对象
 * tileHeight: 平铺高度
**/
TRIGLAV_PLUGIN_API *getTileHeightProc(
    TriglavPlugInOffscreenObject offscreenObject, 
    TriglavPlugInInt* tileHeight);
```

18. 从指定的离屏对象创建并获取位图对象

```cpp
/**
 * offscreenObject: 离屏对象
 * offscreenPos: 离屏左上角坐标
 * bitmapPos: 位图左上角坐标
 * copyWidth: 复制宽度
 * copyHeight: 复制高度
 * copyMode: 复制模式
 * bitmapObject: 位图对象
**/
TRIGLAV_PLUGIN_API *getBitmapProc(
    TriglavPlugInOffscreenObject offscreenObject, 
    const TriglavPlugInPoint* offscreenPos, 
    const TriglavPlugInPoint* bitmapPos, 
    const TriglavPlugInInt copyWidth, 
    const TriglavPlugInInt copyHeight, 
    const TriglavPlugInInt copyMode, 
    TriglavPlugInBitmapObject bitmapObject);
```

复制模式取下列常量之一：

```cpp
#define    kTriglavPlugInOffscreenCopyModeNormal                 (0x01)
#define    kTriglavPlugInOffscreenCopyModeImage                  (0x02)
#define    kTriglavPlugInOffscreenCopyModeGray                   (0x03)
#define    kTriglavPlugInOffscreenCopyModeRed                    (0x04)
#define    kTriglavPlugInOffscreenCopyModeGreen                  (0x05)
#define    kTriglavPlugInOffscreenCopyModeBlue                   (0x06)
#define    kTriglavPlugInOffscreenCopyModeCyan                   (0x07)
#define    kTriglavPlugInOffscreenCopyModeMagenta                (0x08)
#define    kTriglavPlugInOffscreenCopyModeYellow                 (0x09)
#define    kTriglavPlugInOffscreenCopyModeKeyPlate               (0x10)
#define    kTriglavPlugInOffscreenCopyModeAlpha                  (0x11)
```

19. 根据指定的位图对象设置离屏对象

```cpp
/**
 * bitmapObject: 位图对象
 * bitmapPos: 位图左上角坐标
 * copyWidth: 复制宽度
 * copyHeight: 复制高度
 * copyMode: 复制模式
 * offscreenObject: 离屏对象
 * offscreenPos: 离屏左上角坐标
**/
TRIGLAV_PLUGIN_API *setBitmapProc(
    TriglavPlugInBitmapObject bitmapObject, 
    const TriglavPlugInPoint* bitmapPos, 
    const TriglavPlugInInt copyWidth, 
    const TriglavPlugInInt copyHeight, 
    const TriglavPlugInInt copyMode, 
    TriglavPlugInOffscreenObject offscreenObject, 
    TriglavPlugInPoint* offscreenPos);
```

# 四、离屏服务2

该服务主要提供处理离屏的功能，结构体定义如下：

```cpp
typedef struct _TriglavPlugInOffscreenService2 {
    TriglavPlugInOffscreenGetBitmapNormalAlphaChannelIndexProc   getBitmapNormalAlphaChannelIndexProc;
} TriglavPlugInOffscreenService2;
```

1. 获取复制模式为 **kTriglavPlugInOffscreenCopyModeNormal** 时的 **channel index**

```cpp
/**
 * offscreenObject: 离屏对象
 * alphaChannelIndex: alpha channel index
**/
TRIGLAV_PLUGIN_API *getBitmapNormalAlphaChannelIndexProc(
    TriglavPlugInOffscreenObject offscreenObject, 
    TriglavPlugInInt* alphaChannelIndex);
```

# 五、属性服务

该服务主要提供处理过滤器中被使用的参数的功能。结构体定义如下：

```cpp
typedef	struct	_TriglavPlugInPropertyService {
	TriglavPlugInPropertyCreateProc					   createProc;
	TriglavPlugInPropertyRetainProc					   retainProc;
	TriglavPlugInPropertyReleaseProc				   releaseProc;
	TriglavPlugInPropertyAddItemProc				   addItemProc;
	TriglavPlugInPropertySetBooleanValueProc		   setBooleanValueProc;
	TriglavPlugInPropertyGetBooleanValueProc    	   getBooleanValueProc;
	TriglavPlugInPropertySetBooleanDefaultValueProc    setBooleanDefaultValueProc;
	TriglavPlugInPropertyGetBooleanDefaultValueProc    getBooleanDefaultValueProc;
	TriglavPlugInPropertySetIntegerValueProc		   setIntegerValueProc;
	TriglavPlugInPropertyGetIntegerValueProc           getIntegerValueProc;
	TriglavPlugInPropertySetIntegerDefaultValueProc    setIntegerDefaultValueProc;
	TriglavPlugInPropertyGetIntegerDefaultValueProc    getIntegerDefaultValueProc;
	TriglavPlugInPropertySetIntegerMinValueProc 	   setIntegerMinValueProc;
	TriglavPlugInPropertyGetIntegerMinValueProc		   getIntegerMinValueProc;
	TriglavPlugInPropertySetIntegerMaxValueProc		   setIntegerMaxValueProc;
	TriglavPlugInPropertyGetIntegerMaxValueProc		   getIntegerMaxValueProc;
	TriglavPlugInPropertySetDecimalValueProc		   setDecimalValueProc;
	TriglavPlugInPropertyGetDecimalValueProc		   getDecimalValueProc;
	TriglavPlugInPropertySetDecimalDefaultValueProc	   setDecimalDefaultValueProc;
	TriglavPlugInPropertyGetDecimalDefaultValueProc	   getDecimalDefaultValueProc;
	TriglavPlugInPropertySetDecimalMinValueProc		   setDecimalMinValueProc;
	TriglavPlugInPropertyGetDecimalMinValueProc		   getDecimalMinValueProc;
	TriglavPlugInPropertySetDecimalMaxValueProc		   setDecimalMaxValueProc;
	TriglavPlugInPropertyGetDecimalMaxValueProc		   getDecimalMaxValueProc;
} TriglavPlugInPropertyService;
```

1. 创建一个属性对象，不需要时需手动调用函数 **releaceProc**() 销毁。

```cpp
/**
 * propertyObject: 属性对象
**/
TRIGLAV_PLUGIN_API *createProc(
    TriglavPlugInPropertyObject* propertyObject);
```

2. 销毁属性对象

```cpp
/**
 * propertyObject: 属性对象
**/
TRIGLAV_PLUGIN_API *releaceProc(
    TriglavPlugInPropertyObject* propertyObject);
```

3. 将项目添加导致到指定的属性对象

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * valueType: 值类型
 * valueKind: 值种类
 * inputKind: 输入种类
 * caption: 标题
 * accessKey: 访问密钥
**/
TRIGLAV_PLUGIN_API *addItemProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInInt valueType, 
    const TriglavPlugInInt valueKind, 
    const TriglavPlugInInt inputKind, 
    const TriglavPlugInStringObject caption, 
    const TriglavPlugInChar accessKey);
```

其中 **valueType**、**valueKind** 和 **inputKind** 分别从以下常量中获取：

```cpp
// value type
#define    kTriglavPlugInPropertyValueTypeVoid                 (0x00)
#define    kTriglavPlugInPropertyValueTypeBoolean              (0x01)
#define    kTriglavPlugInPropertyValueTypeEnumeration          (0x02)
#define    kTriglavPlugInPropertyValueTypeInteger              (0x11)
#define    kTriglavPlugInPropertyValueTypeDecimal              (0x12)
#define    kTriglavPlugInPropertyValueTypePoint                (0x21)
#define    kTriglavPlugInPropertyValueTypeString               (0x31)
// value kind
#define    kTriglavPlugInPropertyValueKindDefault              (0x11)
#define    kTriglavPlugInPropertyValueKindPixel                (0x21)
// input kind
#define    kTriglavPlugInPropertyInputKindHide                 (0x10)
#define    kTriglavPlugInPropertyInputKindDefault              (0x11)
#define    kTriglavPlugInPropertyInputKindPushButton           (0x21)
#define    kTriglavPlugInPropertyInputKindCanvas               (0x31)
```

4. 对指定属性对象的指定 itemKey 设定真值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * value: 真值
**/
TRIGLAV_PLUGIN_API *setBooleanValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInBool value);
```

5. 获取指定属性对象的指定 **itemKey** 的真值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * value: 真值
**/
TRIGLAV_PLUGIN_API *getBooleanValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInBool* value);
```

6. 对指定属性对象的指定 **itemKey** 设定默认真值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * defaultValue: 默认真值
**/
TRIGLAV_PLUGIN_API *setBooleanDefaultValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInBool defaultValue);
```

7. 获取指定属性对象的指定 **itemKey** 的默认真值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * defaultValue: 默认真值
**/
TRIGLAV_PLUGIN_API *getBooleanDefaultValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInBool* defaultValue);
```

8. 对指定属性对象的指定 **itemKey** 设置整数值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * value: 整数值
**/
TRIGLAV_PLUGIN_API *setIntegerValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInInt value);
```

9. 获取指定属性对象的指定 **itemKey** 的整数值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * value: 整数值
**/
TRIGLAV_PLUGIN_API *getIntegerValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInInt* value);
```

10. 对指定属性对象的指定 **itemKey** 设置默认整数值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * defaultValue: 默认整数值
**/
TRIGLAV_PLUGIN_API *setIntegerDefaultValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInInt defaultValue);
```

11. 获取指定属性对象的指定 **itemKey** 的默认整数值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * defaultValue: 默认整数值
**/
TRIGLAV_PLUGIN_API *getIntegerDefaultValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInInt* defaultValue);
```

12. 对指定属性对象的指定 **itemKey** 设置最小整数值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * minValue: 最小整数值
**/
TRIGLAV_PLUGIN_API *setIntegerMinValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInInt minValue);
```

13. 获取指定属性对象的指定 **itemKey** 的最小整数值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * minValue: 最小整数值
**/
TRIGLAV_PLUGIN_API *getIntegerMinValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInInt* minValue);
```

14. 对指定属性对象的指定 **itemKey** 设置最大整数值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * maxValue: 最大整数值
**/
TRIGLAV_PLUGIN_API *setIntegerMaxValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInInt maxValue);
```

15. 获取指定属性对象的指定 **itemKey** 的最大整数值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * maxValue: 最大整数值
**/
TRIGLAV_PLUGIN_API *getIntegerMaxValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInInt* maxValue);
```

16. 对指定属性对象的指定 **itemKey** 设置小数值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * value: 小数值
**/
TRIGLAV_PLUGIN_API *setDecimalValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInDouble value);
```

17. 获取指定属性对象的指定 **itemKey** 的小数值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * value: 小数值
**/
TRIGLAV_PLUGIN_API *getDecimalValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInDouble* value);
```

18. 对指定属性对象的指定 **itemKey** 设置默认小数值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * defaultValue: 默认小数值
**/
TRIGLAV_PLUGIN_API *setDecimalDefaultValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInDouble defaultValue);
```

19. 获取指定属性对象的指定 **itemKey** 的默认小数值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * defaultValue: 默认小数值
**/
TRIGLAV_PLUGIN_API *getDecimalDefaultValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInDouble* defaultValue);
```

20. 对指定属性对象的指定 **itemKey** 设置最小小数值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * minValue: 最小小数值
**/
TRIGLAV_PLUGIN_API *setDecimalMinValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInDouble minValue);
```

21. 获取指定属性对象的指定 **itemKey** 的最小小数值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * minValue: 最小小数值
**/
TRIGLAV_PLUGIN_API *getDecimalMinValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInDouble* minValue);
```

22. 对指定属性对象的指定 **itemKey** 设置最大小数值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * maxValue: 最大小数值
**/
TRIGLAV_PLUGIN_API *setDecimalMaxValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInDouble maxValue);
```

23.  获取指定属性对象的指定 **itemKey** 的最大小数值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * maxValue: 最大小数值
**/
TRIGLAV_PLUGIN_API *getDecimalMaxValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInDouble* maxValue);
```



# 六、属性服务2

该服务继续提供处理过滤器中被使用的参数的功能。结构体定义如下：

```cpp
typedef	struct	_TriglavPlugInPropertyService2 {
	TriglavPlugInPropertySetItemStoreValueProc			    setItemStoreValueProc;
	TriglavPlugInPropertySetPointValueProc				    setPointValueProc;
	TriglavPlugInPropertyGetPointValueProc				    getPointValueProc;
	TriglavPlugInPropertySetPointDefaultValueKindProc       setPointDefaultValueKindProc;
	TriglavPlugInPropertyGetPointDefaultValueKindProc       getPointDefaultValueKindProc;
	TriglavPlugInPropertySetPointDefaultValueProc		    setPointDefaultValueProc;
	TriglavPlugInPropertyGetPointDefaultValueProc		    getPointDefaultValueProc;
	TriglavPlugInPropertySetPointMinMaxValueKindProc    	setPointMinMaxValueKindProc;
	TriglavPlugInPropertyGetPointMinMaxValueKindProc	    getPointMinMaxValueKindProc;
	TriglavPlugInPropertySetPointMinValueProc				setPointMinValueProc;
	TriglavPlugInPropertyGetPointMinValueProc				getPointMinValueProc;
	TriglavPlugInPropertySetPointMaxValueProc				setPointMaxValueProc;
	TriglavPlugInPropertyGetPointMaxValueProc				getPointMaxValueProc;
	TriglavPlugInPropertySetEnumerationValueProc		    setEnumerationValueProc;
	TriglavPlugInPropertyGetEnumerationValueProc		    getEnumerationValueProc;
	TriglavPlugInPropertySetEnumerationDefaultValueProc     setEnumerationDefaultValueProc;
	TriglavPlugInPropertyGetEnumerationDefaultValueProc		getEnumerationDefaultValueProc;
	TriglavPlugInPropertyAddEnumerationItemProc				addEnumerationItemProc;
	TriglavPlugInPropertySetStringValueProc					setStringValueProc;
	TriglavPlugInPropertyGetStringValueProc					getStringValueProc;
	TriglavPlugInPropertySetStringDefaultValueProc			setStringDefaultValueProc;
	TriglavPlugInPropertyGetStringDefaultValueProc			getStringDefaultValueProc;
	TriglavPlugInPropertySetStringMaxLengthProc				setStringMaxLengthProc;
	TriglavPlugInPropertyGetStringMaxLengthProc			    getStringMaxLengthProc;
} TriglavPlugInPropertyService2;
```

1. 设置是否存储指定属性对象的指定 **itemKey** 的值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * storeValue: 存储项目值
**/
TRIGLAV_PLUGIN_API *setItemStoreValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInBool storeValue);
```

2. 对指定属性对象的指定 **itemKey** 设置坐标

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * value: 坐标
**/
TRIGLAV_PLUGIN_API *setPointValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInPoint* value);
```

3. 获取指定属性对象的指定 **itemKey** 的坐标

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * value: 坐标
**/
TRIGLAV_PLUGIN_API *getPointValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInPoint* value);
```

4. 对指定属性对象的指定 **itemKey** 设置默认坐标类型

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * defaultValueKind: 默认坐标类型
**/
TRIGLAV_PLUGIN_API *setPointDefaultValueKindProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInInt defaultValueKind);
```

其中 **defaultValueKind** 取值于下列常量之一：

```cpp
#define kTriglavPlugInPropertyPointDefaultValueKindDefault               (0x11)
#define kTriglavPlugInPropertyPointDefaultValueKindCanvasLeftTop         (0x21)
#define kTriglavPlugInPropertyPointDefaultValueKindCanvasRightTop        (0x22)
#define kTriglavPlugInPropertyPointDefaultValueKindCanvasLeftBottom      (0x23)
#define kTriglavPlugInPropertyPointDefaultValueKindCanvasRightBottom     (0x24)
#define kTriglavPlugInPropertyPointDefaultValueKindCanvasCenter          (0x25)
#define kTriglavPlugInPropertyPointDefaultValueKindSelectAreaLeftTop     (0x31)
#define kTriglavPlugInPropertyPointDefaultValueKindSelectAreaRightTop    (0x32)
#define kTriglavPlugInPropertyPointDefaultValueKindSelectAreaLeftBottom  (0x33)
#define kTriglavPlugInPropertyPointDefaultValueKindSelectAreaRightBottom (0x34)
#define kTriglavPlugInPropertyPointDefaultValueKindSelectAreaCenter      (0x35)
```

5. 获取指定属性对象的指定 **itemKey** 的默认坐标类型

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * defaultValueKind: 默认坐标类型
**/
TRIGLAV_PLUGIN_API *getPointDefaultValueKindProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInInt* defaultValueKind);
```

6. 对指定属性对象的指定 **itemKey** 设置默认坐标

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * defaultValue: 默认坐标
**/
TRIGLAV_PLUGIN_API *setPointDefaultValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInPoint* defaultValue);
```

7. 获取指定属性对象的指定 **itemKey** 的默认坐标

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * defaultValue: 默认坐标
**/
TRIGLAV_PLUGIN_API *getPointDefaultValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInPoint* defaultValue);
```

8. 对指定属性对象的指定 **itemKey** 设置坐标最小最大值类型

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * minMaxValueKind: 坐标最小最大值类型
**/
TRIGLAV_PLUGIN_API *setPointMinMaxValueKindProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInInt minMaxValueKind);
```

坐标最小最大值类型一般取下列常量之一：

```cpp
#define    kTriglavPlugInPropertyPointMinMaxValueKindDefault      (0x21)
#define    kTriglavPlugInPropertyPointMinMaxValueKindNo           (0x22)
```

9. 获取指定属性对象的指定 **itemKey** 的坐标最小最大值类型

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * minMaxValueKind: 坐标最小最大值类型
**/
TRIGLAV_PLUGIN_API *getPointMinMaxValueKindProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInInt* minMaxValueKind);
```

10. 对指定属性对象的指定 **itemKey** 设置最小坐标

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * minValue: 最小坐标
**/
TRIGLAV_PLUGIN_API *setPointMinValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInPoint* minValue);
```

11. 获取指定属性对象的指定 **itemKey** 的最小坐标

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * minValue: 最小坐标
**/
TRIGLAV_PLUGIN_API *getPointMinValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInPoint* minValue);
```

12. 对指定属性对象的指定 **itemKey** 设置最大坐标

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * maxValue: 最大坐标
**/
TRIGLAV_PLUGIN_API *setPointMaxValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInPoint* maxValue);
```

13. 获取指定属性对象的指定 **itemKey** 的最大坐标

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * maxValue: 最大坐标
**/
TRIGLAV_PLUGIN_API *getPointMaxValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInPoint* maxValue);
```

14. 对指定属性对象的指定 itemKey 设置当前枚举值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * value: 当前枚举
**/
TRIGLAV_PLUGIN_API *setEnumerationValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInInt value);
```

对于可设置的 **numerationValue**，需先执行函数 **addEnumerationItemProc**() 添加。

15. 获取指定属性对象的指定 **itemKey** 的当前枚举值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * value: 当前枚举
**/
TRIGLAV_PLUGIN_API *getEnumerationValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInInt* value);
```

16. 对指定属性对象的指定 **itemKey** 设置默认枚举值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * value: 默认枚举值
**/
TRIGLAV_PLUGIN_API *setEnumerationDefaultValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInInt value);
```

对于可设置的 **numerationValue**，需先执行函数 **addEnumerationItemProc**() 添加。

17. 获取指定属性对象的指定 **itemKey** 的默认枚举值

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * value: 默认枚举值
**/
TRIGLAV_PLUGIN_API *getEnumerationDefaultValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInInt* value);
```

18. 对指定属性对象的指定 **itemKey** 添加枚举项

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * value: 枚举项
 * caption: 字符串对象
 * accessKey: 访问密钥
**/
TRIGLAV_PLUGIN_API *addEnumerationItemProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInInt value, 
    TriglavPlugInStringObject caption, 
    const TriglavPlugInChar accessKey);
```

19. 对指定属性对象的指定 **itemKey** 设置字符串

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * value: 字符串
**/
TRIGLAV_PLUGIN_API *setStringValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInStringObject value);
```

20. 获取指定属性对象的指定 **itemKey** 的字符串

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * value: 字符串
**/
TRIGLAV_PLUGIN_API *getStringValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInStringObject* value);
```

21. 对指定属性对象的指定 **itemKey** 设置默认字符串

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * defaultValue: 默认字符串
**/
TRIGLAV_PLUGIN_API *setStringDefaultValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInStringObject defaultValue);
```

22. 获取指定属性对象的指定 **itemKey** 的默认字符串

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * defaultValue: 默认字符串
**/
TRIGLAV_PLUGIN_API *getStringDefaultValueProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInStringObject* defaultValue);
```

23. 对指定属性对象的指定 **itemKey** 设置字符串最大字符数

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * maxLength: 字符串最大字符数
**/
TRIGLAV_PLUGIN_API *setStringMaxLengthProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInInt maxLength);
```

24. 获取指定属性对象的指定 **itemKey** 的字符串最大字符数

```cpp
/**
 * propertyObject: 属性对象
 * itemKey: 项目键
 * maxLength: 字符串最大字符数
**/
TRIGLAV_PLUGIN_API *getStringMaxLengthProc(
    TriglavPlugInPropertyObject* propertyObject, 
    const TriglavPlugInInt itemKey, 
    TriglavPlugInInt* maxLength);
```

**clip studio paint** 插件开发的全部 **api** 介绍完毕。
