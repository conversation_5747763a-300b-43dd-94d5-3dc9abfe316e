#pragma once

#include <QColor>
#include <QVector3D>
#include <cmath>

// 颜色结构体定义
struct HSVColor {
    double h, s, v;  // h: 0-360, s,v: 0-100
    HSVColor(double h = 0, double s = 0, double v = 0) : h(h), s(s), v(v) {}
    
    bool operator==(const HSVColor& other) const {
        return qFuzzyCompare(h, other.h) && qFuzzyCompare(s, other.s) && qFuzzyCompare(v, other.v);
    }
};

struct HSLColor {
    double h, s, l;  // h: 0-360, s,l: 0-100
    HSLColor(double h = 0, double s = 0, double l = 0) : h(h), s(s), l(l) {}
    
    bool operator==(const HSLColor& other) const {
        return qFuzzyCompare(h, other.h) && qFuzzyCompare(s, other.s) && qFuzzyCompare(l, other.l);
    }
};

struct LABColor {
    double l, a, b;  // l: 0-100, a,b: -128 to 127
    LABColor(double l = 0, double a = 0, double b = 0) : l(l), a(a), b(b) {}
    
    bool operator==(const LABColor& other) const {
        return qFuzzyCompare(l, other.l) && qFuzzyCompare(a, other.a) && qFuzzyCompare(b, other.b);
    }
};

struct CMYKColor {
    double c, m, y, k;  // 0-100
    CMYKColor(double c = 0, double m = 0, double y = 0, double k = 0) : c(c), m(m), y(y), k(k) {}
    
    bool operator==(const CMYKColor& other) const {
        return qFuzzyCompare(c, other.c) && qFuzzyCompare(m, other.m) && 
               qFuzzyCompare(y, other.y) && qFuzzyCompare(k, other.k);
    }
};

struct RYBColor {
    double r, y, b;  // 0-100
    RYBColor(double r = 0, double y = 0, double b = 0) : r(r), y(y), b(b) {}
    
    bool operator==(const RYBColor& other) const {
        return qFuzzyCompare(r, other.r) && qFuzzyCompare(y, other.y) && qFuzzyCompare(b, other.b);
    }
};

// 颜色空间枚举
enum class ColorSpace {
    RGB = 0,
    HSV = 1,
    HSL = 2,
    LAB = 3,
    CMYK = 4,
    RYB = 5
};

// 色域限制枚举
enum class ColorGamut {
    None = 0,        // 无限制
    sRGB = 1,        // sRGB
    AdobeRGB = 2,    // Adobe RGB
    ProPhotoRGB = 3, // ProPhoto RGB
    Rec2020 = 4,     // Rec. 2020
    Custom = 5       // 自定义
};

// 颜色转换工具类
class ColorConverter {
public:
    // RGB转换函数
    static HSVColor rgbToHsv(const QColor& rgb);
    static HSLColor rgbToHsl(const QColor& rgb);
    static LABColor rgbToLab(const QColor& rgb);
    static CMYKColor rgbToCmyk(const QColor& rgb);
    static RYBColor rgbToRyb(const QColor& rgb);
    
    // 转换到RGB函数
    static QColor hsvToRgb(const HSVColor& hsv);
    static QColor hslToRgb(const HSLColor& hsl);
    static QColor labToRgb(const LABColor& lab);
    static QColor cmykToRgb(const CMYKColor& cmyk);
    static QColor rybToRgb(const RYBColor& ryb);
    
    // 色域限制函数
    static QColor clampToGamut(const QColor& color, ColorGamut gamut);
    static bool isInGamut(const QColor& color, ColorGamut gamut);
    
    // 颜色距离计算
    static double colorDistance(const QColor& color1, const QColor& color2, ColorSpace space = ColorSpace::LAB);
    static double deltaE(const LABColor& lab1, const LABColor& lab2);
    static double deltaE2000(const LABColor& lab1, const LABColor& lab2);
    
    // 颜色混合
    static QColor mixColors(const QColor& color1, const QColor& color2, double ratio, ColorSpace space = ColorSpace::RGB);
    static QColor blendColors(const QColor& base, const QColor& overlay, double opacity);
    
    // 颜色调整
    static QColor adjustHue(const QColor& color, double hueShift, ColorSpace space = ColorSpace::HSV);
    static QColor adjustSaturation(const QColor& color, double saturationFactor, ColorSpace space = ColorSpace::HSV);
    static QColor adjustLightness(const QColor& color, double lightnessFactor, ColorSpace space = ColorSpace::HSL);
    static QColor adjustContrast(const QColor& color, double contrastFactor);
    static QColor adjustBrightness(const QColor& color, double brightnessFactor);
    
    // 颜色分析
    static double getLuminance(const QColor& color);
    static double getContrastRatio(const QColor& color1, const QColor& color2);
    static bool isAccessible(const QColor& foreground, const QColor& background, double minRatio = 4.5);
    
    // 颜色格式转换
    static QString colorToHex(const QColor& color, bool includeAlpha = false);
    static QColor hexToColor(const QString& hex);
    static QString colorToCss(const QColor& color, ColorSpace space = ColorSpace::RGB);
    static QColor cssToColor(const QString& css);
    
    // 颜色温度
    static QColor temperatureToRgb(double kelvin);
    static double rgbToTemperature(const QColor& rgb);
    
    // 颜色名称
    static QString getColorName(const QColor& color);
    static QColor getNamedColor(const QString& name);
    
    // 实用函数
    static QVector<QColor> generateGradient(const QColor& start, const QColor& end, int steps, ColorSpace space = ColorSpace::RGB);
    static QColor getComplementaryColor(const QColor& color, ColorSpace space = ColorSpace::HSV);
    static QVector<QColor> getAnalogousColors(const QColor& color, int count = 3, double angle = 30.0, ColorSpace space = ColorSpace::HSV);
    
private:
    // 辅助函数
    static double hueToRgb(double p, double q, double t);
    static double xyzToLab(double t);
    static double labToXyz(double t);
    static QVector3D rgbToXyz(const QColor& rgb);
    static QColor xyzToRgb(const QVector3D& xyz);
    static double normalizeHue(double hue);
    static double clamp(double value, double min = 0.0, double max = 1.0);
    
    // 色域矩阵
    static const double sRGB_MATRIX[3][3];
    static const double ADOBE_RGB_MATRIX[3][3];
    static const double PROPHOTO_RGB_MATRIX[3][3];
    static const double REC2020_MATRIX[3][3];
    
    // 白点定义
    static const QVector3D D50_WHITE_POINT;
    static const QVector3D D65_WHITE_POINT;
    
    // 常量
    static constexpr double EPSILON = 0.008856;
    static constexpr double KAPPA = 903.3;
    static constexpr double PI = 3.14159265358979323846;
    static constexpr double DEG_TO_RAD = PI / 180.0;
    static constexpr double RAD_TO_DEG = 180.0 / PI;
};
