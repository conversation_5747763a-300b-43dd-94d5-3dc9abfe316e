#pragma once

#define WIN32_LEAN_AND_MEAN             // Exclude rarely-used stuff from Windows headers
// Windows Header Files
#include <windows.h>
#include <comdef.h>

// C RunTime Header Files
#include <stdlib.h>
#include <malloc.h>
#include <memory.h>
#include <tchar.h>
#include <math.h>
#include <algorithm>
#include <vector>
#include <memory>

// CSP Plugin SDK Headers
#include "TriglavPlugInSDK/TriglavPlugInSDK.h"
