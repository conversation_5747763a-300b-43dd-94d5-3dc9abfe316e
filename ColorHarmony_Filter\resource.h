//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ generated include file.
// Used by ColorHarmony_Filter.rc

// String IDs for localization
#define IDS_FILTER_CATEGORY_NAME        201
#define IDS_FILTER_NAME                 202
#define IDS_COLOR_SPACE                 203
#define IDS_HARMONY_TYPE                204
#define IDS_BASE_HUE                    205
#define IDS_BASE_SATURATION             206
#define IDS_BASE_LIGHTNESS              207
#define IDS_LOCK_LIGHTNESS              208
#define IDS_COLOR_GAMUT                 209
#define IDS_APPLY_MODE                  210
#define IDS_PREVIEW                     211

// Color Space Options
#define IDS_CS_RGB                      301
#define IDS_CS_HSV                      302
#define IDS_CS_HSL                      303
#define IDS_CS_LAB                      304
#define IDS_CS_CMYK                     305
#define IDS_CS_RYB                      306

// Harmony Type Options
#define IDS_HT_COMPLEMENTARY            401
#define IDS_HT_ANALOGOUS                402
#define IDS_HT_TRIADIC                  403
#define IDS_HT_TETRADIC                 404
#define IDS_HT_SPLIT_COMPLEMENTARY      405
#define IDS_HT_MONOCHROMATIC            406

// Color Gamut Options
#define IDS_CG_NO_LIMIT                 501
#define IDS_CG_SRGB                     502
#define IDS_CG_ADOBE_RGB                503
#define IDS_CG_CUSTOM                   504

// Apply Mode Options
#define IDS_AM_REPLACE                  601
#define IDS_AM_BLEND                    602
#define IDS_AM_GRADIENT                 603

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NEXT_RESOURCE_VALUE        101
#define _APS_NEXT_COMMAND_VALUE         40001
#define _APS_NEXT_CONTROL_VALUE         1001
#define _APS_NEXT_SYMED_VALUE           101
#endif
#endif
