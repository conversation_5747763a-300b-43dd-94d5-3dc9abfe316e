#pragma once

#include <QAbstractListModel>
#include <QColor>
#include <QString>
#include <QDateTime>
#include <QStringList>
#include <QJsonObject>
#include <QJsonArray>
#include <QMimeData>
#include <QUndoStack>
#include <QUndoCommand>
#include <QPixmap>
#include <QIcon>
#include <QVariant>
#include <QModelIndex>
#include <memory>

// 颜色条目结构
struct ColorEntry {
    QColor color;
    QString name;
    QString description;
    QStringList tags;
    QDateTime created;
    QDateTime modified;
    bool locked;        // 是否锁定（不可编辑）
    bool favorite;      // 是否收藏
    double usage;       // 使用频率
    QVariant userData;  // 用户自定义数据
    
    ColorEntry() 
        : color(Qt::white)
        , created(QDateTime::currentDateTime())
        , modified(QDateTime::currentDateTime())
        , locked(false)
        , favorite(false)
        , usage(0.0)
    {}
    
    ColorEntry(const QColor& c, const QString& n = QString())
        : color(c)
        , name(n)
        , created(QDateTime::currentDateTime())
        , modified(QDateTime::currentDateTime())
        , locked(false)
        , favorite(false)
        , usage(0.0)
    {}
    
    bool operator==(const ColorEntry& other) const {
        return color == other.color && name == other.name;
    }
};

// 调色盘元数据
struct PaletteMetadata {
    QString name;
    QString description;
    QString author;
    QString version;
    QStringList tags;
    QDateTime created;
    QDateTime modified;
    QString filePath;
    bool readOnly;
    int colorCount;
    
    PaletteMetadata()
        : created(QDateTime::currentDateTime())
        , modified(QDateTime::currentDateTime())
        , readOnly(false)
        , colorCount(0)
    {}
};

// 调色盘模型
class PaletteModel : public QAbstractListModel
{
    Q_OBJECT
    
public:
    // 自定义角色
    enum Roles {
        ColorRole = Qt::UserRole + 1,
        NameRole,
        DescriptionRole,
        TagsRole,
        CreatedRole,
        ModifiedRole,
        LockedRole,
        FavoriteRole,
        UsageRole,
        UserDataRole,
        HexRole,
        RgbRole,
        HsvRole,
        HslRole,
        LabRole,
        CmykRole
    };
    
    // 排序方式
    enum SortOrder {
        SortByName,
        SortByHue,
        SortByLightness,
        SortBySaturation,
        SortByCreated,
        SortByUsage,
        SortByCustom
    };
    
    explicit PaletteModel(QObject *parent = nullptr);
    ~PaletteModel();
    
    // QAbstractListModel接口
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole) override;
    Qt::ItemFlags flags(const QModelIndex &index) const override;
    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;
    
    // 拖放支持
    Qt::DropActions supportedDropActions() const override;
    QStringList mimeTypes() const override;
    QMimeData *mimeData(const QModelIndexList &indexes) const override;
    bool dropMimeData(const QMimeData *data, Qt::DropAction action, int row, int column, const QModelIndex &parent) override;
    
    // 颜色操作
    void addColor(const QColor& color, const QString& name = QString());
    void addColor(const ColorEntry& entry);
    void addColors(const QVector<ColorEntry>& entries);
    void insertColor(int index, const QColor& color, const QString& name = QString());
    void insertColor(int index, const ColorEntry& entry);
    void removeColor(int index);
    void removeColors(const QVector<int>& indices);
    void updateColor(int index, const QColor& color);
    void updateColor(int index, const ColorEntry& entry);
    void moveColor(int from, int to);
    void swapColors(int index1, int index2);
    void clear();
    
    // 查询操作
    int colorCount() const;
    ColorEntry getColorEntry(int index) const;
    QColor getColor(int index) const;
    QString getColorName(int index) const;
    int findColor(const QColor& color, double tolerance = 0.0) const;
    int findColorByName(const QString& name) const;
    QVector<int> findColorsByTag(const QString& tag) const;
    QVector<int> findSimilarColors(const QColor& color, double tolerance = 10.0) const;
    
    // 批量操作
    void setColors(const QVector<ColorEntry>& entries);
    QVector<ColorEntry> getColors() const;
    QVector<QColor> getColorList() const;
    void duplicateColors(const QVector<int>& indices);
    void lockColors(const QVector<int>& indices, bool locked = true);
    void favoriteColors(const QVector<int>& indices, bool favorite = true);
    
    // 排序和过滤
    void sortColors(SortOrder order, Qt::SortOrder direction = Qt::AscendingOrder);
    void shuffleColors();
    void reverseColors();
    void setFilter(const QString& filter);
    void clearFilter();
    
    // 元数据操作
    PaletteMetadata getMetadata() const;
    void setMetadata(const PaletteMetadata& metadata);
    void updateMetadata();
    
    // 文件操作
    bool saveToFile(const QString& filePath);
    bool loadFromFile(const QString& filePath);
    bool isModified() const;
    void setModified(bool modified = true);
    QString getCurrentFilePath() const;
    
    // 导入导出
    bool exportToASE(const QString& filePath) const;    // Adobe Swatch Exchange
    bool exportToGPL(const QString& filePath) const;    // GIMP Palette
    bool exportToACO(const QString& filePath) const;    // Adobe Color
    bool exportToSKP(const QString& filePath) const;    // Sketch Palette
    bool exportToJSON(const QString& filePath) const;   // JSON格式
    bool exportToCSS(const QString& filePath) const;    // CSS变量
    bool exportToSASS(const QString& filePath) const;   // SASS变量
    
    bool importFromASE(const QString& filePath);
    bool importFromGPL(const QString& filePath);
    bool importFromACO(const QString& filePath);
    bool importFromJSON(const QString& filePath);
    bool importFromImage(const QString& filePath, int maxColors = 16);
    
    // 撤销重做
    QUndoStack* getUndoStack() const;
    void pushCommand(QUndoCommand* command);
    
    // 统计信息
    QMap<QString, int> getColorStatistics() const;
    QVector<QColor> getMostUsedColors(int count = 10) const;
    QVector<QColor> getRecentColors(int count = 10) const;
    double getAverageHue() const;
    double getAverageSaturation() const;
    double getAverageLightness() const;

signals:
    void colorAdded(int index, const ColorEntry& entry);
    void colorRemoved(int index, const ColorEntry& entry);
    void colorChanged(int index, const ColorEntry& entry);
    void colorsSwapped(int index1, int index2);
    void colorsMoved(int from, int to);
    void paletteCleared();
    void metadataChanged(const PaletteMetadata& metadata);
    void modifiedChanged(bool modified);
    void filterChanged(const QString& filter);
    void sortOrderChanged(SortOrder order, Qt::SortOrder direction);

public slots:
    void onColorUsed(int index);
    void onColorUsed(const QColor& color);

private:
    // 内部数据
    QVector<ColorEntry> m_colors;
    QVector<ColorEntry> m_filteredColors;
    PaletteMetadata m_metadata;
    bool m_modified;
    QString m_filter;
    SortOrder m_sortOrder;
    Qt::SortOrder m_sortDirection;
    
    // 撤销重做
    std::unique_ptr<QUndoStack> m_undoStack;
    
    // 辅助函数
    void applyFilter();
    void applySorting();
    bool matchesFilter(const ColorEntry& entry, const QString& filter) const;
    QIcon createColorIcon(const QColor& color, const QSize& size = QSize(16, 16)) const;
    QString generateColorName(const QColor& color) const;
    
    // 序列化
    QJsonObject entryToJson(const ColorEntry& entry) const;
    ColorEntry entryFromJson(const QJsonObject& json) const;
    QJsonObject metadataToJson(const PaletteMetadata& metadata) const;
    PaletteMetadata metadataFromJson(const QJsonObject& json) const;
    
    // 文件格式处理
    bool saveAsJSON(const QString& filePath) const;
    bool loadFromJSON(const QString& filePath);
    
    // 常量
    static const QString MIME_TYPE_COLOR_LIST;
    static const QString MIME_TYPE_COLOR_ENTRY;
    static const int MAX_RECENT_COLORS = 50;
};
