# Color Harmony - 独立调色盘应用

## 项目概述

Color Harmony 是一个专业的独立调色盘应用，专为数字艺术家和设计师设计。它提供了强大的颜色和谐算法、多种色彩空间支持、以及直观的用户界面。

## 技术架构

### 技术栈选择

**主要技术栈：Qt 6 + C++**
- ✅ 跨平台支持 (Windows, macOS, Linux)
- ✅ 丰富的UI组件和自定义能力
- ✅ 优秀的性能和内存管理
- ✅ 强大的图形和绘制能力
- ✅ 成熟的生态系统

**替代方案对比：**
1. **Electron + Web技术**
   - ✅ 快速开发，丰富的Web生态
   - ❌ 内存占用大，性能相对较低
   
2. **原生Windows API + Win32**
   - ✅ 最佳性能，完全控制
   - ❌ 只支持Windows，开发复杂度高
   
3. **Flutter Desktop**
   - ✅ 现代UI，跨平台
   - ❌ 生态相对较新，C++集成复杂

### 应用架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Color Harmony Application                │
├─────────────────────────────────────────────────────────────┤
│  Presentation Layer (Qt Widgets/QML)                       │
│  ┌─────────────────┐ ┌─────────────────┐ ┌───────────────┐ │
│  │   Main Window   │ │  Color Picker   │ │  Palette Mgr  │ │
│  │   - Menu Bar    │ │  - Color Wheel  │ │  - Save/Load  │ │
│  │   - Tool Bar    │ │  - Sliders      │ │  - Import/Exp │ │
│  │   - Status Bar  │ │  - Input Fields │ │  - Templates  │ │
│  └─────────────────┘ └─────────────────┘ └───────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Business Logic Layer                                       │
│  ┌─────────────────┐ ┌─────────────────┐ ┌───────────────┐ │
│  │ Harmony Engine  │ │ Color Converter │ │ Palette Model │ │
│  │ - Complementary │ │ - RGB ↔ HSV     │ │ - Data Struct │ │
│  │ - Analogous     │ │ - RGB ↔ HSL     │ │ - Validation  │ │
│  │ - Triadic       │ │ - RGB ↔ LAB     │ │ - Serialization│ │
│  │ - Tetradic      │ │ - RGB ↔ CMYK    │ │ - History     │ │
│  │ - Split Comp    │ │ - RGB ↔ RYB     │ │ - Undo/Redo   │ │
│  └─────────────────┘ └─────────────────┘ └───────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  System Integration Layer                                   │
│  ┌─────────────────┐ ┌─────────────────┐ ┌───────────────┐ │
│  │ Clipboard Mgr   │ │ File System     │ │ Settings Mgr  │ │
│  │ - Copy Colors   │ │ - Palette Files │ │ - User Prefs  │ │
│  │ - Paste Colors  │ │ - Export Formats│ │ - Themes      │ │
│  │ - Format Conv   │ │ - Recent Files  │ │ - Shortcuts   │ │
│  └─────────────────┘ └─────────────────┘ └───────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 核心功能模块

### 1. 颜色和谐引擎 (HarmonyEngine)
```cpp
class HarmonyEngine {
public:
    enum HarmonyType {
        Complementary,      // 互补色
        Analogous,          // 邻近色  
        Triadic,            // 三角色
        Tetradic,           // 四角色
        SplitComplementary, // 分裂互补色
        Monochromatic       // 单色调
    };
    
    std::vector<QColor> generateHarmony(
        const QColor& baseColor,
        HarmonyType type,
        ColorSpace colorSpace = RGB
    );
};
```

### 2. 颜色转换器 (ColorConverter)
```cpp
class ColorConverter {
public:
    // 支持的色彩空间
    enum ColorSpace { RGB, HSV, HSL, LAB, CMYK, RYB };
    
    // 转换函数
    static HSVColor rgbToHsv(const RGBColor& rgb);
    static HSLColor rgbToHsl(const RGBColor& rgb);
    static LABColor rgbToLab(const RGBColor& rgb);
    static CMYKColor rgbToCmyk(const RGBColor& rgb);
    static RYBColor rgbToRyb(const RGBColor& rgb);
    
    // 反向转换
    static RGBColor hsvToRgb(const HSVColor& hsv);
    static RGBColor hslToRgb(const HSLColor& hsl);
    static RGBColor labToRgb(const LABColor& lab);
    static RGBColor cmykToRgb(const CMYKColor& cmyk);
    static RGBColor rybToRgb(const RYBColor& ryb);
};
```

### 3. 调色盘模型 (PaletteModel)
```cpp
class PaletteModel : public QAbstractListModel {
    Q_OBJECT
    
public:
    struct ColorEntry {
        QColor color;
        QString name;
        QDateTime created;
        QStringList tags;
    };
    
    // 调色盘操作
    void addColor(const QColor& color, const QString& name = "");
    void removeColor(int index);
    void updateColor(int index, const QColor& color);
    
    // 文件操作
    bool saveToFile(const QString& filePath);
    bool loadFromFile(const QString& filePath);
    
    // 导入导出
    bool exportToASE(const QString& filePath);  // Adobe Swatch Exchange
    bool exportToGPL(const QString& filePath);  // GIMP Palette
    bool exportToACO(const QString& filePath);  // Adobe Color
};
```

## 用户界面设计

### 主窗口布局
```
┌─────────────────────────────────────────────────────────────┐
│ File Edit View Tools Help                          [_][□][×]│
├─────────────────────────────────────────────────────────────┤
│ [New] [Open] [Save] │ [Undo] [Redo] │ [Copy] [Paste]       │
├─────────────────────┴───────────────┴─────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────┐ │
│ │   Color Wheel   │ │         Harmony Colors              │ │
│ │                 │ │  ┌───┐ ┌───┐ ┌───┐ ┌───┐ ┌───┐     │ │
│ │       ●         │ │  │ 1 │ │ 2 │ │ 3 │ │ 4 │ │ 5 │     │ │
│ │                 │ │  └───┘ └───┘ └───┘ └───┘ └───┘     │ │
│ └─────────────────┘ └─────────────────────────────────────┘ │
│ ┌─────────────────┐ ┌─────────────────────────────────────┐ │
│ │  Color Values   │ │         Current Palette             │ │
│ │  RGB: 255,0,128 │ │  ┌─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┐   │ │
│ │  HSV: 330,100,50│ │  │ │ │ │ │ │ │ │ │ │ │ │ │ │ │ │   │ │
│ │  HSL: 330,100,25│ │  └─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┘   │ │
│ │  LAB: 50,75,-25 │ │  ┌─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┐   │ │
│ │  CMYK: 0,100,50,0│ │  │ │ │ │ │ │ │ │ │ │ │ │ │ │ │ │   │ │
│ │  RYB: 255,0,128 │ │  └─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┘   │ │
│ └─────────────────┘ └─────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Harmony: Complementary │ Space: RGB │ Gamut: sRGB │ Ready  │
└─────────────────────────────────────────────────────────────┘
```

## 数据流设计

### 颜色选择流程
```
用户交互 → ColorPicker → ColorConverter → HarmonyEngine → PaletteModel → UI更新
    ↓           ↓              ↓              ↓             ↓
  鼠标点击   获取RGB值    转换色彩空间    计算和谐色    更新调色盘   刷新显示
```

### 文件操作流程
```
用户操作 → FileManager → PaletteModel → 序列化/反序列化 → 文件系统
    ↓          ↓            ↓              ↓              ↓
  保存/加载   验证格式    更新数据模型    JSON/XML格式    磁盘读写
```

## 开发计划

### Phase 1: 核心架构 (Week 1-2)
- [x] 项目结构设计
- [ ] Qt项目搭建
- [ ] 基础窗口和布局
- [ ] 颜色转换算法实现

### Phase 2: 核心功能 (Week 3-4)
- [ ] 颜色和谐算法引擎
- [ ] 高级颜色选择器组件
- [ ] 调色盘数据模型

### Phase 3: 用户界面 (Week 5-6)
- [ ] 主窗口界面实现
- [ ] 颜色选择器UI
- [ ] 调色盘管理界面

### Phase 4: 系统集成 (Week 7-8)
- [ ] 剪贴板集成
- [ ] 文件系统操作
- [ ] 设置和配置管理

### Phase 5: 测试优化 (Week 9-10)
- [ ] 功能测试
- [ ] 性能优化
- [ ] 用户体验改进
- [ ] 文档编写

## 技术要求

- **开发环境**: Qt 6.5+, C++17, CMake 3.20+
- **目标平台**: Windows 10+, macOS 10.15+, Ubuntu 20.04+
- **依赖库**: Qt6 Core/Widgets/Gui, 可选QML
- **构建工具**: CMake, Qt Creator或Visual Studio

## 文件结构
```
ColorHarmony_Standalone/
├── src/
│   ├── main.cpp
│   ├── MainWindow.h/cpp
│   ├── core/
│   │   ├── ColorConverter.h/cpp
│   │   ├── HarmonyEngine.h/cpp
│   │   └── PaletteModel.h/cpp
│   ├── widgets/
│   │   ├── ColorPicker.h/cpp
│   │   ├── ColorWheel.h/cpp
│   │   └── PaletteWidget.h/cpp
│   └── utils/
│       ├── FileManager.h/cpp
│       └── ClipboardManager.h/cpp
├── resources/
│   ├── icons/
│   ├── themes/
│   └── palettes/
├── tests/
├── docs/
└── CMakeLists.txt
```
