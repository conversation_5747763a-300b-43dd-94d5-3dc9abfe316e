#pragma once

#include "TriglavPlugInSDK/TriglavPlugInSDK.h"
#include <vector>
#include <memory>

// 颜色结构体定义
struct RGBColor
{
    double r, g, b;  // 0.0 - 1.0
    RGBColor(double r = 0, double g = 0, double b = 0) : r(r), g(g), b(b) {}
};

struct HSVColor
{
    double h, s, v;  // h: 0-360, s,v: 0-100
    HSVColor(double h = 0, double s = 0, double v = 0) : h(h), s(s), v(v) {}
};

struct HSLColor
{
    double h, s, l;  // h: 0-360, s,l: 0-100
    HSLColor(double h = 0, double s = 0, double l = 0) : h(h), s(s), l(l) {}
};

struct LABColor
{
    double l, a, b;  // l: 0-100, a,b: -128 to 127
    LABColor(double l = 0, double a = 0, double b = 0) : l(l), a(a), b(b) {}
};

struct CMYKColor
{
    double c, m, y, k;  // 0-100
    CMYKColor(double c = 0, double m = 0, double y = 0, double k = 0) : c(c), m(m), y(y), k(k) {}
};

struct RYBColor
{
    double r, y, b;  // 0-100
    RYBColor(double r = 0, double y = 0, double b = 0) : r(r), y(y), b(b) {}
};

// 颜色空间枚举
enum ColorSpace
{
    CS_RGB = 0,
    CS_HSV = 1,
    CS_HSL = 2,
    CS_LAB = 3,
    CS_CMYK = 4,
    CS_RYB = 5
};

// 和谐类型枚举
enum HarmonyType
{
    HT_COMPLEMENTARY = 0,    // 互补色
    HT_ANALOGOUS = 1,        // 邻近色
    HT_TRIADIC = 2,          // 三角色
    HT_TETRADIC = 3,         // 四角色
    HT_SPLIT_COMPLEMENTARY = 4, // 分裂互补色
    HT_MONOCHROMATIC = 5     // 单色调
};

// 色域限制枚举
enum ColorGamut
{
    CG_NONE = 0,        // 无限制
    CG_SRGB = 1,        // sRGB
    CG_ADOBE_RGB = 2,   // Adobe RGB
    CG_CUSTOM = 3       // 自定义
};

// 应用模式枚举
enum ApplyMode
{
    AM_REPLACE = 0,     // 替换
    AM_BLEND = 1,       // 混合
    AM_GRADIENT = 2     // 渐变
};

// 颜色转换工具类
class ColorConverter
{
public:
    // RGB转换函数
    static HSVColor RGBToHSV(const RGBColor& rgb);
    static HSLColor RGBToHSL(const RGBColor& rgb);
    static LABColor RGBToLAB(const RGBColor& rgb);
    static CMYKColor RGBToCMYK(const RGBColor& rgb);
    static RYBColor RGBToRYB(const RGBColor& rgb);
    
    // 转换到RGB函数
    static RGBColor HSVToRGB(const HSVColor& hsv);
    static RGBColor HSLToRGB(const HSLColor& hsl);
    static RGBColor LABToRGB(const LABColor& lab);
    static RGBColor CMYKToRGB(const CMYKColor& cmyk);
    static RGBColor RYBToRGB(const RYBColor& ryb);
    
    // 色域限制函数
    static RGBColor ClampToGamut(const RGBColor& color, ColorGamut gamut);
    
private:
    // 辅助函数
    static double HueToRGB(double p, double q, double t);
    static double XYZToLAB(double t);
    static double LABToXYZ(double t);
};

// 颜色和谐计算器
class HarmonyCalculator
{
public:
    // 计算和谐色彩
    static std::vector<RGBColor> CalculateHarmony(
        const RGBColor& baseColor,
        HarmonyType harmonyType,
        ColorSpace colorSpace,
        bool lockLightness = false
    );
    
    // 计算互补色
    static std::vector<RGBColor> CalculateComplementary(const RGBColor& baseColor, ColorSpace colorSpace);
    
    // 计算邻近色
    static std::vector<RGBColor> CalculateAnalogous(const RGBColor& baseColor, ColorSpace colorSpace);
    
    // 计算三角色
    static std::vector<RGBColor> CalculateTriadic(const RGBColor& baseColor, ColorSpace colorSpace);
    
    // 计算四角色
    static std::vector<RGBColor> CalculateTetradic(const RGBColor& baseColor, ColorSpace colorSpace);
    
    // 计算分裂互补色
    static std::vector<RGBColor> CalculateSplitComplementary(const RGBColor& baseColor, ColorSpace colorSpace);
    
    // 计算单色调
    static std::vector<RGBColor> CalculateMonochromatic(const RGBColor& baseColor, ColorSpace colorSpace);
    
private:
    // 在指定色彩空间中调整色相
    static RGBColor AdjustHueInColorSpace(const RGBColor& color, double hueOffset, ColorSpace colorSpace);
};

// 主要的颜色和谐引擎类
class ColorHarmony
{
public:
    static ColorHarmony& GetInstance();
    static void PluginModuleCleanUp();
    
    // 初始化和清理
    bool Init(TriglavPlugInServer* pluginServer);
    void ShutDown();
    
    // 参数更新回调
    void OnParametersChanged(
        int colorSpace,
        int harmonyType,
        double baseHue,
        double baseSaturation,
        double baseLightness,
        bool lockLightness,
        int colorGamut,
        int applyMode
    );
    
    // 渲染处理
    void Render();
    
    // 设置跳过处理
    void SetSkip(bool skip);
    
private:
    ColorHarmony();
    ~ColorHarmony();
    
    // 禁用拷贝构造和赋值
    ColorHarmony(const ColorHarmony&) = delete;
    ColorHarmony& operator=(const ColorHarmony&) = delete;
    
    // 内部状态
    TriglavPlugInServer* m_pPluginServer;
    bool m_bInitialized;
    bool m_bSkip;
    
    // 当前参数
    ColorSpace m_currentColorSpace;
    HarmonyType m_currentHarmonyType;
    RGBColor m_baseColor;
    bool m_lockLightness;
    ColorGamut m_colorGamut;
    ApplyMode m_applyMode;
    
    // 计算出的和谐色彩
    std::vector<RGBColor> m_harmonyColors;
    
    // 内部处理函数
    void UpdateHarmonyColors();
    void ApplyColorsToImage();
    RGBColor GetCurrentDrawColor();
    void SetCurrentDrawColor(const RGBColor& color);
    
    // 图像处理函数
    void ProcessImageData(
        TriglavPlugInOffscreenObject sourceOffscreen,
        TriglavPlugInOffscreenObject destOffscreen,
        const TriglavPlugInRect& processRect
    );
    
    // 静态实例
    static std::unique_ptr<ColorHarmony> s_instance;
};
