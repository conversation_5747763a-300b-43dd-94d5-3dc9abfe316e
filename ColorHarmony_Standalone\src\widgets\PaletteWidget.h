#pragma once

#include <QWidget>
#include <QListView>
#include <QGridLayout>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QScrollArea>
#include <QLabel>
#include <QPushButton>
#include <QToolButton>
#include <QMenu>
#include <QAction>
#include <QContextMenuEvent>
#include <QDragEnterEvent>
#include <QDropEvent>
#include <QMimeData>
#include <QPropertyAnimation>
#include <QParallelAnimationGroup>
#include <QSequentialAnimationGroup>
#include <QEasingCurve>
#include <QTimer>
#include <QRubberBand>
#include <QItemSelectionModel>
#include <QStyledItemDelegate>
#include <QPainter>
#include <QStyleOptionViewItem>
#include <QApplication>
#include <QClipboard>

#include "../core/PaletteModel.h"

// 现代化颜色项委托 - Windows 11风格
class ColorItemDelegate : public QStyledItemDelegate
{
    Q_OBJECT

public:
    explicit ColorItemDelegate(QObject *parent = nullptr);

    void paint(QPain<PERSON> *painter, const QStyleOptionViewItem &option, const QModelIndex &index) const override;
    QSize sizeHint(const QStyleOptionViewItem &option, const QModelIndex &index) const override;
    
    // 自定义属性
    void setItemSize(const QSize& size);
    void setItemSpacing(int spacing);
    void setShowColorNames(bool show);
    void setShowColorValues(bool show);
    void setCornerRadius(int radius);

protected:
    bool editorEvent(QEvent *event, QAbstractItemModel *model, const QStyleOptionViewItem &option, const QModelIndex &index) override;

private:
    void drawColorSwatch(QPainter *painter, const QRect &rect, const QColor &color, bool selected, bool hovered) const;
    void drawColorInfo(QPainter *painter, const QRect &rect, const QModelIndex &index, bool selected) const;
    void drawSelectionIndicator(QPainter *painter, const QRect &rect) const;
    void drawHoverEffect(QPainter *painter, const QRect &rect) const;
    
    QSize m_itemSize;
    int m_itemSpacing;
    bool m_showColorNames;
    bool m_showColorValues;
    int m_cornerRadius;
    
    static const int DEFAULT_ITEM_SIZE = 48;
    static const int DEFAULT_SPACING = 4;
    static const int DEFAULT_CORNER_RADIUS = 8;
};

// 现代化调色盘组件
class PaletteWidget : public QWidget
{
    Q_OBJECT
    Q_PROPERTY(ViewMode viewMode READ viewMode WRITE setViewMode NOTIFY viewModeChanged)
    Q_PROPERTY(int itemSize READ itemSize WRITE setItemSize NOTIFY itemSizeChanged)
    Q_PROPERTY(bool showColorNames READ showColorNames WRITE setShowColorNames)
    Q_PROPERTY(bool showColorValues READ showColorValues WRITE setShowColorValues)
    Q_PROPERTY(bool allowDragDrop READ allowDragDrop WRITE setAllowDragDrop)

public:
    enum ViewMode {
        GridView,
        ListView,
        CompactView,
        DetailView
    };
    Q_ENUM(ViewMode)

    explicit PaletteWidget(QWidget *parent = nullptr);
    ~PaletteWidget();

    // 模型相关
    void setModel(PaletteModel *model);
    PaletteModel *model() const;
    
    // 视图属性
    ViewMode viewMode() const { return m_viewMode; }
    int itemSize() const { return m_itemSize; }
    bool showColorNames() const { return m_showColorNames; }
    bool showColorValues() const { return m_showColorValues; }
    bool allowDragDrop() const { return m_allowDragDrop; }
    
    // 选择相关
    QModelIndexList selectedIndexes() const;
    QVector<QColor> selectedColors() const;
    int selectedCount() const;
    bool hasSelection() const;
    
    // 查找和过滤
    void setFilter(const QString& filter);
    QString filter() const;
    void clearFilter();

public slots:
    void setViewMode(ViewMode mode);
    void setItemSize(int size);
    void setShowColorNames(bool show);
    void setShowColorValues(bool show);
    void setAllowDragDrop(bool allow);
    
    // 选择操作
    void selectAll();
    void clearSelection();
    void selectColor(const QColor& color);
    void selectIndex(const QModelIndex& index);
    void selectRange(int start, int end);
    
    // 编辑操作
    void addColor(const QColor& color);
    void removeSelectedColors();
    void duplicateSelectedColors();
    void copySelectedColors();
    void pasteColors();
    void editSelectedColor();
    
    // 视图操作
    void zoomIn();
    void zoomOut();
    void resetZoom();
    void fitToWindow();
    void scrollToColor(const QColor& color);
    void scrollToIndex(const QModelIndex& index);
    
    // 动画效果
    void animateColorAddition(const QColor& color);
    void animateColorRemoval(const QModelIndex& index);
    void animateColorChange(const QModelIndex& index);

signals:
    void colorClicked(const QColor& color, const QModelIndex& index);
    void colorDoubleClicked(const QColor& color, const QModelIndex& index);
    void colorRightClicked(const QColor& color, const QModelIndex& index, const QPoint& globalPos);
    void selectionChanged();
    void viewModeChanged(ViewMode mode);
    void itemSizeChanged(int size);
    void filterChanged(const QString& filter);
    void colorsDropped(const QVector<QColor>& colors, int position);

protected:
    void contextMenuEvent(QContextMenuEvent* event) override;
    void dragEnterEvent(QDragEnterEvent* event) override;
    void dragMoveEvent(QDragMoveEvent* event) override;
    void dropEvent(QDropEvent* event) override;
    void wheelEvent(QWheelEvent* event) override;
    void keyPressEvent(QKeyEvent* event) override;
    void resizeEvent(QResizeEvent* event) override;

private slots:
    void onItemClicked(const QModelIndex& index);
    void onItemDoubleClicked(const QModelIndex& index);
    void onSelectionChanged();
    void onModelDataChanged();
    void onContextMenuRequested(const QPoint& pos);
    void onAnimationFinished();

private:
    void setupUI();
    void setupConnections();
    void createContextMenu();
    void updateViewMode();
    void updateItemDelegate();
    void updateLayout();
    
    // 动画相关
    void setupAnimations();
    void startAddAnimation(const QModelIndex& index);
    void startRemoveAnimation(const QModelIndex& index);
    void startChangeAnimation(const QModelIndex& index);
    
    // 拖放处理
    bool canDropMimeData(const QMimeData* data) const;
    QVector<QColor> extractColorsFromMimeData(const QMimeData* data) const;
    int getDropPosition(const QPoint& pos) const;
    
    // UI组件
    QVBoxLayout* m_mainLayout;
    QListView* m_listView;
    QScrollArea* m_scrollArea;
    QLabel* m_emptyLabel;
    
    // 数据和委托
    PaletteModel* m_model;
    ColorItemDelegate* m_delegate;
    
    // 属性
    ViewMode m_viewMode;
    int m_itemSize;
    bool m_showColorNames;
    bool m_showColorValues;
    bool m_allowDragDrop;
    QString m_filter;
    
    // 上下文菜单
    QMenu* m_contextMenu;
    QAction* m_addColorAction;
    QAction* m_removeColorAction;
    QAction* m_duplicateColorAction;
    QAction* m_editColorAction;
    QAction* m_copyColorAction;
    QAction* m_pasteColorAction;
    QAction* m_selectAllAction;
    QAction* m_clearSelectionAction;
    
    // 动画
    QPropertyAnimation* m_addAnimation;
    QPropertyAnimation* m_removeAnimation;
    QPropertyAnimation* m_changeAnimation;
    QParallelAnimationGroup* m_animationGroup;
    
    // 拖放指示器
    QRubberBand* m_dropIndicator;
    int m_dropPosition;
    
    // 常量
    static const int MIN_ITEM_SIZE = 24;
    static const int MAX_ITEM_SIZE = 128;
    static const int DEFAULT_ITEM_SIZE = 48;
    static const int ZOOM_STEP = 8;
    static const int ANIMATION_DURATION = 250;
};
