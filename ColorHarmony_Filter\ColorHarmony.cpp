#include "pch.h"
#include "ColorHarmony.h"
#include <algorithm>
#include <cmath>

// 静态实例
std::unique_ptr<ColorHarmony> ColorHarmony::s_instance = nullptr;

// 数学常量
const double PI = 3.14159265358979323846;
const double DEG_TO_RAD = PI / 180.0;
const double RAD_TO_DEG = 180.0 / PI;

// ColorConverter 实现
HSVColor ColorConverter::RGBToHSV(const RGBColor& rgb)
{
    double r = rgb.r, g = rgb.g, b = rgb.b;
    double max_val = std::max({r, g, b});
    double min_val = std::min({r, g, b});
    double delta = max_val - min_val;
    
    HSVColor hsv;
    
    // 计算色相 (Hue)
    if (delta == 0) {
        hsv.h = 0;
    } else if (max_val == r) {
        hsv.h = 60 * fmod(((g - b) / delta), 6);
    } else if (max_val == g) {
        hsv.h = 60 * (((b - r) / delta) + 2);
    } else {
        hsv.h = 60 * (((r - g) / delta) + 4);
    }
    
    if (hsv.h < 0) hsv.h += 360;
    
    // 计算饱和度 (Saturation)
    hsv.s = (max_val == 0) ? 0 : (delta / max_val) * 100;
    
    // 计算明度 (Value)
    hsv.v = max_val * 100;
    
    return hsv;
}

HSLColor ColorConverter::RGBToHSL(const RGBColor& rgb)
{
    double r = rgb.r, g = rgb.g, b = rgb.b;
    double max_val = std::max({r, g, b});
    double min_val = std::min({r, g, b});
    double delta = max_val - min_val;
    
    HSLColor hsl;
    
    // 计算亮度 (Lightness)
    hsl.l = ((max_val + min_val) / 2) * 100;
    
    if (delta == 0) {
        hsl.h = 0;
        hsl.s = 0;
    } else {
        // 计算饱和度 (Saturation)
        hsl.s = (hsl.l > 50) ? 
            (delta / (2 - max_val - min_val)) * 100 : 
            (delta / (max_val + min_val)) * 100;
        
        // 计算色相 (Hue)
        if (max_val == r) {
            hsl.h = 60 * fmod(((g - b) / delta), 6);
        } else if (max_val == g) {
            hsl.h = 60 * (((b - r) / delta) + 2);
        } else {
            hsl.h = 60 * (((r - g) / delta) + 4);
        }
        
        if (hsl.h < 0) hsl.h += 360;
    }
    
    return hsl;
}

LABColor ColorConverter::RGBToLAB(const RGBColor& rgb)
{
    // 首先转换到XYZ色彩空间
    double r = rgb.r, g = rgb.g, b = rgb.b;
    
    // Gamma校正
    r = (r > 0.04045) ? pow((r + 0.055) / 1.055, 2.4) : r / 12.92;
    g = (g > 0.04045) ? pow((g + 0.055) / 1.055, 2.4) : g / 12.92;
    b = (b > 0.04045) ? pow((b + 0.055) / 1.055, 2.4) : b / 12.92;
    
    // 转换到XYZ (使用D65白点)
    double x = r * 0.4124564 + g * 0.3575761 + b * 0.1804375;
    double y = r * 0.2126729 + g * 0.7151522 + b * 0.0721750;
    double z = r * 0.0193339 + g * 0.1191920 + b * 0.9503041;
    
    // 标准化到D65白点
    x /= 0.95047;
    y /= 1.00000;
    z /= 1.08883;
    
    // 转换到LAB
    x = XYZToLAB(x);
    y = XYZToLAB(y);
    z = XYZToLAB(z);
    
    LABColor lab;
    lab.l = (116 * y) - 16;
    lab.a = 500 * (x - y);
    lab.b = 200 * (y - z);
    
    return lab;
}

CMYKColor ColorConverter::RGBToCMYK(const RGBColor& rgb)
{
    double r = rgb.r, g = rgb.g, b = rgb.b;
    
    // 计算K (黑色)
    double k = 1 - std::max({r, g, b});
    
    CMYKColor cmyk;
    cmyk.k = k * 100;
    
    if (k == 1) {
        cmyk.c = cmyk.m = cmyk.y = 0;
    } else {
        cmyk.c = ((1 - r - k) / (1 - k)) * 100;
        cmyk.m = ((1 - g - k) / (1 - k)) * 100;
        cmyk.y = ((1 - b - k) / (1 - k)) * 100;
    }
    
    return cmyk;
}

RYBColor ColorConverter::RGBToRYB(const RGBColor& rgb)
{
    // RYB转换使用近似算法
    double r = rgb.r, g = rgb.g, b = rgb.b;
    
    // 移除白色成分
    double w = std::min({r, g, b});
    r -= w; g -= w; b -= w;
    
    double mg = std::max({r, g, b});
    
    // 获取黄色成分
    double y = std::min(r, g);
    r -= y; g -= y;
    
    // 如果这个过程产生了蓝色和绿色，那么将它们转换为蓝色和黄色
    if (b > 0 && g > 0) {
        b += g;
        g = 0;
    }
    
    // 重新分配红色到黄色和红色
    if (b > 0 && r > 0) {
        if (b > r) {
            b -= r;
            r = 0;
        } else {
            r -= b;
            b = 0;
        }
    }
    
    RYBColor ryb;
    ryb.r = (r + w) * 100;
    ryb.y = (y + w) * 100;
    ryb.b = (b + w) * 100;
    
    return ryb;
}

RGBColor ColorConverter::HSVToRGB(const HSVColor& hsv)
{
    double h = hsv.h, s = hsv.s / 100.0, v = hsv.v / 100.0;
    
    double c = v * s;
    double x = c * (1 - abs(fmod(h / 60.0, 2) - 1));
    double m = v - c;
    
    double r, g, b;
    
    if (h >= 0 && h < 60) {
        r = c; g = x; b = 0;
    } else if (h >= 60 && h < 120) {
        r = x; g = c; b = 0;
    } else if (h >= 120 && h < 180) {
        r = 0; g = c; b = x;
    } else if (h >= 180 && h < 240) {
        r = 0; g = x; b = c;
    } else if (h >= 240 && h < 300) {
        r = x; g = 0; b = c;
    } else {
        r = c; g = 0; b = x;
    }
    
    return RGBColor(r + m, g + m, b + m);
}

RGBColor ColorConverter::HSLToRGB(const HSLColor& hsl)
{
    double h = hsl.h, s = hsl.s / 100.0, l = hsl.l / 100.0;
    
    if (s == 0) {
        return RGBColor(l, l, l);
    }
    
    double q = (l < 0.5) ? l * (1 + s) : l + s - l * s;
    double p = 2 * l - q;
    
    double r = HueToRGB(p, q, (h + 120) / 360.0);
    double g = HueToRGB(p, q, h / 360.0);
    double b = HueToRGB(p, q, (h - 120) / 360.0);
    
    return RGBColor(r, g, b);
}

double ColorConverter::HueToRGB(double p, double q, double t)
{
    if (t < 0) t += 1;
    if (t > 1) t -= 1;
    if (t < 1.0/6.0) return p + (q - p) * 6 * t;
    if (t < 1.0/2.0) return q;
    if (t < 2.0/3.0) return p + (q - p) * (2.0/3.0 - t) * 6;
    return p;
}

double ColorConverter::XYZToLAB(double t)
{
    return (t > 0.008856) ? pow(t, 1.0/3.0) : (7.787 * t + 16.0/116.0);
}

double ColorConverter::LABToXYZ(double t)
{
    double t3 = t * t * t;
    return (t3 > 0.008856) ? t3 : (t - 16.0/116.0) / 7.787;
}

RGBColor ColorConverter::LABToRGB(const LABColor& lab)
{
    double l = lab.l, a = lab.a, b = lab.b;

    // 转换到XYZ
    double y = (l + 16) / 116.0;
    double x = a / 500.0 + y;
    double z = y - b / 200.0;

    x = LABToXYZ(x) * 0.95047;
    y = LABToXYZ(y) * 1.00000;
    z = LABToXYZ(z) * 1.08883;

    // 转换到RGB
    double r = x *  3.2404542 + y * -1.5371385 + z * -0.4985314;
    double g = x * -0.9692660 + y *  1.8760108 + z *  0.0415560;
    double b_rgb = x *  0.0556434 + y * -0.2040259 + z *  1.0572252;

    // Gamma校正
    r = (r > 0.0031308) ? 1.055 * pow(r, 1.0/2.4) - 0.055 : 12.92 * r;
    g = (g > 0.0031308) ? 1.055 * pow(g, 1.0/2.4) - 0.055 : 12.92 * g;
    b_rgb = (b_rgb > 0.0031308) ? 1.055 * pow(b_rgb, 1.0/2.4) - 0.055 : 12.92 * b_rgb;

    // 限制范围
    r = std::max(0.0, std::min(1.0, r));
    g = std::max(0.0, std::min(1.0, g));
    b_rgb = std::max(0.0, std::min(1.0, b_rgb));

    return RGBColor(r, g, b_rgb);
}

RGBColor ColorConverter::CMYKToRGB(const CMYKColor& cmyk)
{
    double c = cmyk.c / 100.0;
    double m = cmyk.m / 100.0;
    double y = cmyk.y / 100.0;
    double k = cmyk.k / 100.0;

    double r = (1 - c) * (1 - k);
    double g = (1 - m) * (1 - k);
    double b = (1 - y) * (1 - k);

    return RGBColor(r, g, b);
}

RGBColor ColorConverter::RYBToRGB(const RYBColor& ryb)
{
    // RYB到RGB的近似转换
    double r = ryb.r / 100.0;
    double y = ryb.y / 100.0;
    double b = ryb.b / 100.0;

    // 移除白色成分
    double w = std::min({r, y, b});
    r -= w; y -= w; b -= w;

    double my = std::max({r, y, b});

    // 获取绿色成分
    double g = std::min(r, y);
    r -= g; y -= g;

    // 如果这个过程产生了蓝色和黄色，那么将它们转换为蓝色和绿色
    if (b > 0 && y > 0) {
        g += y;
        y = 0;
    }

    // 重新分配红色到黄色和红色
    if (b > 0 && r > 0) {
        if (b > r) {
            b -= r;
            r = 0;
        } else {
            r -= b;
            b = 0;
        }
    }

    return RGBColor(r + w, g + w, b + w);
}

RGBColor ColorConverter::ClampToGamut(const RGBColor& color, ColorGamut gamut)
{
    RGBColor result = color;

    switch (gamut) {
        case CG_SRGB:
            // sRGB色域限制
            result.r = std::max(0.0, std::min(1.0, result.r));
            result.g = std::max(0.0, std::min(1.0, result.g));
            result.b = std::max(0.0, std::min(1.0, result.b));
            break;

        case CG_ADOBE_RGB:
            // Adobe RGB色域限制 (简化处理)
            result.r = std::max(0.0, std::min(1.0, result.r));
            result.g = std::max(0.0, std::min(1.0, result.g));
            result.b = std::max(0.0, std::min(1.0, result.b));
            break;

        case CG_CUSTOM:
            // 自定义色域限制 (可以根据需要实现)
            result.r = std::max(0.0, std::min(1.0, result.r));
            result.g = std::max(0.0, std::min(1.0, result.g));
            result.b = std::max(0.0, std::min(1.0, result.b));
            break;

        case CG_NONE:
        default:
            // 无限制，但仍然限制在0-1范围内
            result.r = std::max(0.0, std::min(1.0, result.r));
            result.g = std::max(0.0, std::min(1.0, result.g));
            result.b = std::max(0.0, std::min(1.0, result.b));
            break;
    }

    return result;
}
