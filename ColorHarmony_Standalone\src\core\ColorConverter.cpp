#include "ColorConverter.h"
#include <QDebug>
#include <algorithm>
#include <cmath>

// 常量定义
constexpr double ColorConverter::EPSILON;
constexpr double ColorConverter::KAPPA;
constexpr double ColorConverter::PI;
constexpr double ColorConverter::DEG_TO_RAD;
constexpr double ColorConverter::RAD_TO_DEG;

// 白点定义 (D65)
const QVector3D ColorConverter::D65_WHITE_POINT(0.95047, 1.00000, 1.08883);
const QVector3D ColorConverter::D50_WHITE_POINT(0.96422, 1.00000, 0.82521);

// sRGB转换矩阵
const double ColorConverter::sRGB_MATRIX[3][3] = {
    {3.2404542, -1.5371385, -0.4985314},
    {-0.9692660, 1.8760108, 0.0415560},
    {0.0556434, -0.2040259, 1.0572252}
};

// RGB到HSV转换
HSVColor ColorConverter::rgbToHsv(const QColor& rgb)
{
    double r = rgb.redF();
    double g = rgb.greenF();
    double b = rgb.blueF();
    
    double max_val = std::max({r, g, b});
    double min_val = std::min({r, g, b});
    double delta = max_val - min_val;
    
    HSVColor hsv;
    
    // 计算色相 (Hue)
    if (delta == 0) {
        hsv.h = 0;
    } else if (max_val == r) {
        hsv.h = 60 * fmod(((g - b) / delta), 6);
    } else if (max_val == g) {
        hsv.h = 60 * (((b - r) / delta) + 2);
    } else {
        hsv.h = 60 * (((r - g) / delta) + 4);
    }
    
    if (hsv.h < 0) hsv.h += 360;
    
    // 计算饱和度 (Saturation)
    hsv.s = (max_val == 0) ? 0 : (delta / max_val);
    
    // 计算明度 (Value)
    hsv.v = max_val;
    
    return hsv;
}

// RGB到HSL转换
HSLColor ColorConverter::rgbToHsl(const QColor& rgb)
{
    double r = rgb.redF();
    double g = rgb.greenF();
    double b = rgb.blueF();
    
    double max_val = std::max({r, g, b});
    double min_val = std::min({r, g, b});
    double delta = max_val - min_val;
    
    HSLColor hsl;
    
    // 计算亮度 (Lightness)
    hsl.l = (max_val + min_val) / 2;
    
    if (delta == 0) {
        hsl.h = 0;
        hsl.s = 0;
    } else {
        // 计算饱和度 (Saturation)
        hsl.s = (hsl.l > 0.5) ? 
            (delta / (2 - max_val - min_val)) : 
            (delta / (max_val + min_val));
        
        // 计算色相 (Hue)
        if (max_val == r) {
            hsl.h = 60 * fmod(((g - b) / delta), 6);
        } else if (max_val == g) {
            hsl.h = 60 * (((b - r) / delta) + 2);
        } else {
            hsl.h = 60 * (((r - g) / delta) + 4);
        }
        
        if (hsl.h < 0) hsl.h += 360;
    }
    
    return hsl;
}

// RGB到LAB转换
LABColor ColorConverter::rgbToLab(const QColor& rgb)
{
    // 首先转换到XYZ色彩空间
    QVector3D xyz = rgbToXyz(rgb);
    
    // 标准化到D65白点
    double x = xyz.x() / D65_WHITE_POINT.x();
    double y = xyz.y() / D65_WHITE_POINT.y();
    double z = xyz.z() / D65_WHITE_POINT.z();
    
    // 转换到LAB
    x = xyzToLab(x);
    y = xyzToLab(y);
    z = xyzToLab(z);
    
    LABColor lab;
    lab.l = (116 * y) - 16;
    lab.a = 500 * (x - y);
    lab.b = 200 * (y - z);
    
    return lab;
}

// RGB到CMYK转换
CMYKColor ColorConverter::rgbToCmyk(const QColor& rgb)
{
    double r = rgb.redF();
    double g = rgb.greenF();
    double b = rgb.blueF();
    
    // 计算K (黑色)
    double k = 1 - std::max({r, g, b});
    
    CMYKColor cmyk;
    cmyk.k = k * 100;
    
    if (k == 1) {
        cmyk.c = cmyk.m = cmyk.y = 0;
    } else {
        cmyk.c = ((1 - r - k) / (1 - k)) * 100;
        cmyk.m = ((1 - g - k) / (1 - k)) * 100;
        cmyk.y = ((1 - b - k) / (1 - k)) * 100;
    }
    
    return cmyk;
}

// RGB到RYB转换 (艺术家色轮)
RYBColor ColorConverter::rgbToRyb(const QColor& rgb)
{
    double r = rgb.redF();
    double g = rgb.greenF();
    double b = rgb.blueF();
    
    // 移除白色成分
    double w = std::min({r, g, b});
    r -= w; g -= w; b -= w;
    
    // 获取黄色成分
    double y = std::min(r, g);
    r -= y; g -= y;
    
    // 如果有蓝色和绿色，将绿色转换为蓝色
    if (b > 0 && g > 0) {
        b += g;
        g = 0;
    }
    
    // 重新分配红色
    if (b > 0 && r > 0) {
        if (b > r) {
            b -= r;
            r = 0;
        } else {
            r -= b;
            b = 0;
        }
    }
    
    RYBColor ryb;
    ryb.r = (r + w) * 100;
    ryb.y = (y + w) * 100;
    ryb.b = (b + w) * 100;
    
    return ryb;
}

// HSV到RGB转换
QColor ColorConverter::hsvToRgb(const HSVColor& hsv)
{
    double h = normalizeHue(hsv.h);
    double s = clamp(hsv.s, 0.0, 1.0);
    double v = clamp(hsv.v, 0.0, 1.0);
    
    double c = v * s;
    double x = c * (1 - abs(fmod(h / 60.0, 2) - 1));
    double m = v - c;
    
    double r, g, b;
    
    if (h >= 0 && h < 60) {
        r = c; g = x; b = 0;
    } else if (h >= 60 && h < 120) {
        r = x; g = c; b = 0;
    } else if (h >= 120 && h < 180) {
        r = 0; g = c; b = x;
    } else if (h >= 180 && h < 240) {
        r = 0; g = x; b = c;
    } else if (h >= 240 && h < 300) {
        r = x; g = 0; b = c;
    } else {
        r = c; g = 0; b = x;
    }
    
    return QColor::fromRgbF(
        clamp(r + m),
        clamp(g + m),
        clamp(b + m)
    );
}

// HSL到RGB转换
QColor ColorConverter::hslToRgb(const HSLColor& hsl)
{
    double h = normalizeHue(hsl.h);
    double s = clamp(hsl.s, 0.0, 1.0);
    double l = clamp(hsl.l, 0.0, 1.0);
    
    if (s == 0) {
        return QColor::fromRgbF(l, l, l);
    }
    
    double q = (l < 0.5) ? l * (1 + s) : l + s - l * s;
    double p = 2 * l - q;
    
    double r = hueToRgb(p, q, (h + 120) / 360.0);
    double g = hueToRgb(p, q, h / 360.0);
    double b = hueToRgb(p, q, (h - 120) / 360.0);
    
    return QColor::fromRgbF(clamp(r), clamp(g), clamp(b));
}

// 辅助函数：色相到RGB
double ColorConverter::hueToRgb(double p, double q, double t)
{
    if (t < 0) t += 1;
    if (t > 1) t -= 1;
    if (t < 1.0/6.0) return p + (q - p) * 6 * t;
    if (t < 1.0/2.0) return q;
    if (t < 2.0/3.0) return p + (q - p) * (2.0/3.0 - t) * 6;
    return p;
}

// 辅助函数：XYZ到LAB转换
double ColorConverter::xyzToLab(double t)
{
    return (t > EPSILON) ? pow(t, 1.0/3.0) : (KAPPA * t + 16.0) / 116.0;
}

// 辅助函数：LAB到XYZ转换
double ColorConverter::labToXyz(double t)
{
    double t3 = t * t * t;
    return (t3 > EPSILON) ? t3 : (116.0 * t - 16.0) / KAPPA;
}

// RGB到XYZ转换
QVector3D ColorConverter::rgbToXyz(const QColor& rgb)
{
    double r = rgb.redF();
    double g = rgb.greenF();
    double b = rgb.blueF();
    
    // Gamma校正
    r = (r > 0.04045) ? pow((r + 0.055) / 1.055, 2.4) : r / 12.92;
    g = (g > 0.04045) ? pow((g + 0.055) / 1.055, 2.4) : g / 12.92;
    b = (b > 0.04045) ? pow((b + 0.055) / 1.055, 2.4) : b / 12.92;
    
    // 转换到XYZ (使用sRGB矩阵)
    double x = r * 0.4124564 + g * 0.3575761 + b * 0.1804375;
    double y = r * 0.2126729 + g * 0.7151522 + b * 0.0721750;
    double z = r * 0.0193339 + g * 0.1191920 + b * 0.9503041;
    
    return QVector3D(x, y, z);
}

// 标准化色相值
double ColorConverter::normalizeHue(double hue)
{
    while (hue < 0) hue += 360;
    while (hue >= 360) hue -= 360;
    return hue;
}

// 限制数值范围
double ColorConverter::clamp(double value, double min, double max)
{
    return std::max(min, std::min(max, value));
}
