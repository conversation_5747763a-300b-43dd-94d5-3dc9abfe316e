#pragma once

#include <QMainWindow>
#include <QMenuBar>
#include <QToolBar>
#include <QStatusBar>
#include <QSplitter>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QLabel>
#include <QComboBox>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QSlider>
#include <QPushButton>
#include <QCheckBox>
#include <QLineEdit>
#include <QTextEdit>
#include <QListWidget>
#include <QTreeWidget>
#include <QTabWidget>
#include <QProgressBar>
#include <QTimer>
#include <QSettings>
#include <QCloseEvent>
#include <QDragEnterEvent>
#include <QDropEvent>
#include <QMimeData>
#include <QFileDialog>
#include <QMessageBox>
#include <QColorDialog>
#include <QApplication>
#include <QClipboard>

#include "core/HarmonyEngine.h"
#include "core/PaletteModel.h"
#include "widgets/ColorPicker.h"
#include "widgets/ColorWheel.h"
#include "widgets/PaletteWidget.h"
#include "utils/FileManager.h"
#include "utils/ClipboardManager.h"

QT_BEGIN_NAMESPACE
class QAction;
class QActionGroup;
class QMenu;
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void closeEvent(QCloseEvent *event) override;
    void dragEnterEvent(QDragEnterEvent *event) override;
    void dropEvent(QDropEvent *event) override;

private slots:
    // 文件操作
    void newPalette();
    void openPalette();
    void savePalette();
    void saveAsPalette();
    void importPalette();
    void exportPalette();
    void recentFileTriggered();
    
    // 编辑操作
    void undo();
    void redo();
    void copyColor();
    void pasteColor();
    void clearPalette();
    
    // 视图操作
    void toggleColorPicker();
    void togglePalettePanel();
    void toggleHarmonyPanel();
    void resetLayout();
    void toggleFullScreen();
    
    // 工具操作
    void generateHarmony();
    void randomizeColors();
    void sortColors();
    void analyzeImage();
    
    // 设置操作
    void showPreferences();
    void showAbout();
    void showHelp();
    
    // 颜色操作
    void onColorChanged(const QColor &color);
    void onHarmonyTypeChanged(int type);
    void onColorSpaceChanged(int space);
    void onGamutChanged(int gamut);
    void onColorAdded(const QColor &color);
    void onColorRemoved(int index);
    void onPaletteChanged();
    
    // 界面更新
    void updateColorValues(const QColor &color);
    void updateHarmonyColors();
    void updateStatusBar();
    void updateRecentFiles();
    void updateWindowTitle();

private:
    void createActions();
    void createMenus();
    void createToolBars();
    void createStatusBar();
    void createCentralWidget();
    void createDockWidgets();
    void connectSignals();
    void loadSettings();
    void saveSettings();
    void setupShortcuts();
    void setupDragDrop();
    
    // UI组件
    QWidget *m_centralWidget;
    QSplitter *m_mainSplitter;
    QSplitter *m_leftSplitter;
    QSplitter *m_rightSplitter;
    
    // 颜色选择器区域
    QGroupBox *m_colorPickerGroup;
    ColorWheel *m_colorWheel;
    ColorPicker *m_colorPicker;
    
    // 颜色值显示区域
    QGroupBox *m_colorValuesGroup;
    QLabel *m_rgbLabel;
    QLabel *m_hsvLabel;
    QLabel *m_hslLabel;
    QLabel *m_labLabel;
    QLabel *m_cmykLabel;
    QLabel *m_rybLabel;
    QLineEdit *m_hexEdit;
    
    // 和谐色彩区域
    QGroupBox *m_harmonyGroup;
    QComboBox *m_harmonyTypeCombo;
    QComboBox *m_colorSpaceCombo;
    QComboBox *m_gamutCombo;
    QCheckBox *m_lockLightnessCheck;
    QWidget *m_harmonyColorsWidget;
    QHBoxLayout *m_harmonyColorsLayout;
    std::vector<QPushButton*> m_harmonyColorButtons;
    
    // 调色盘区域
    QGroupBox *m_paletteGroup;
    PaletteWidget *m_paletteWidget;
    QLineEdit *m_paletteNameEdit;
    QTextEdit *m_paletteDescEdit;
    
    // 菜单和工具栏
    QMenuBar *m_menuBar;
    QMenu *m_fileMenu;
    QMenu *m_editMenu;
    QMenu *m_viewMenu;
    QMenu *m_toolsMenu;
    QMenu *m_helpMenu;
    QMenu *m_recentFilesMenu;
    
    QToolBar *m_mainToolBar;
    QToolBar *m_colorToolBar;
    QToolBar *m_paletteToolBar;
    
    QStatusBar *m_statusBar;
    QLabel *m_statusLabel;
    QLabel *m_colorCountLabel;
    QLabel *m_harmonyTypeLabel;
    QProgressBar *m_progressBar;
    
    // 动作
    QAction *m_newAction;
    QAction *m_openAction;
    QAction *m_saveAction;
    QAction *m_saveAsAction;
    QAction *m_importAction;
    QAction *m_exportAction;
    QAction *m_exitAction;
    
    QAction *m_undoAction;
    QAction *m_redoAction;
    QAction *m_copyAction;
    QAction *m_pasteAction;
    QAction *m_clearAction;
    
    QAction *m_toggleColorPickerAction;
    QAction *m_togglePalettePanelAction;
    QAction *m_toggleHarmonyPanelAction;
    QAction *m_resetLayoutAction;
    QAction *m_fullScreenAction;
    
    QAction *m_generateHarmonyAction;
    QAction *m_randomizeAction;
    QAction *m_sortAction;
    QAction *m_analyzeImageAction;
    
    QAction *m_preferencesAction;
    QAction *m_aboutAction;
    QAction *m_helpAction;
    
    QActionGroup *m_recentFilesActionGroup;
    
    // 核心组件
    HarmonyEngine *m_harmonyEngine;
    PaletteModel *m_paletteModel;
    FileManager *m_fileManager;
    ClipboardManager *m_clipboardManager;
    
    // 设置和状态
    QSettings *m_settings;
    QString m_currentFilePath;
    bool m_isModified;
    QColor m_currentColor;
    QStringList m_recentFiles;
    
    // 常量
    static const int MAX_RECENT_FILES = 10;
    static const int MAX_HARMONY_COLORS = 8;
    static const QString SETTINGS_GEOMETRY;
    static const QString SETTINGS_STATE;
    static const QString SETTINGS_RECENT_FILES;
    static const QString SETTINGS_LAST_DIR;
};
